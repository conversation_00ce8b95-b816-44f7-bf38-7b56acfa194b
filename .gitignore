# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

#IDE
.vscode
.idea
.iml

# dependencies
node_modules
src/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
src/coverage

# next.js
src/.next/
src/out/

# production
src/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# cloudrun
deploy-frontend-cloudrun.sh

/src/Dockerfile