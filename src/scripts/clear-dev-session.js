#!/usr/bin/env node

/**
 * Development utility to clear NextAuth session cookies
 * Run this script when you encounter "Token is not active" errors
 * 
 * Usage: node scripts/clear-dev-session.js
 */

console.log('🧹 NextAuth Session Cleanup Utility');
console.log('=====================================');
console.log('');
console.log('If you\'re seeing "Token is not active" errors, this usually means:');
console.log('1. Your refresh tokens have expired (typically after 1+ days)');
console.log('2. Keycloak session has been reset/restarted');
console.log('3. Stale session cookies are causing conflicts');
console.log('');
console.log('💡 Quick Fix:');
console.log('1. Open your browser developer tools (F12)');
console.log('2. Go to Application/Storage tab');
console.log('3. Clear all cookies for localhost:3001');
console.log('4. Refresh the page');
console.log('');
console.log('🔧 Alternative:');
console.log('- Use browser incognito/private mode');
console.log('- Or manually delete these cookies:');
console.log('  • next-auth.session-token');
console.log('  • __Secure-next-auth.session-token');
console.log('  • next-auth.csrf-token');
console.log('  • __Host-next-auth.csrf-token');
console.log('');
console.log('✅ After clearing cookies, the middleware will automatically');
console.log('   redirect you to the login page for fresh authentication.');
console.log('');
console.log('🚀 The updated middleware now handles this scenario better');
console.log('   and should automatically clear stale sessions!');
