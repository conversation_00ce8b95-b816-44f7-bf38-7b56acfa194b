import { EChartsWrapper } from '@/components';
import { EChartsOption } from 'echarts';

type PieChartProps = {
  data: { name: string; value: number }[];
};

export function PieChart({ data }: PieChartProps) {
  const options: EChartsOption = {
    title: {
    },
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        name: 'Contract Type',
        type: 'pie',
        radius: '50%',
        data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };

  return (
    <EChartsWrapper
      option={options}
      style={{ height: '300px', width: '100%' }}
    />
  );
}
