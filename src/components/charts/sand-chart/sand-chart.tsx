import { EChartsWrapper } from '@/components';
import { EChartsOption, SeriesOption } from 'echarts';

type SandChartProps = {
  height?: string;
  width?: string;
  programLegends?: string[];
  series: SeriesOption[];
};

export function SandChart({ width, height, series }: SandChartProps) {
  const sandChartOptions: EChartsOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
    },
    grid: {
      left: '0%',
      right: '4%',
      bottom: '0%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        // TODO Make this dynamic
        data: ['2020', '2021', '2022', '2023', '2024', '2025', '2026', '2027'],
      },
    ],
    yAxis: [
      {
        type: 'value',
      },
    ],
    series,
  };

  return (
    <EChartsWrapper
      option={sandChartOptions}
      settings={{ notMerge: true, lazyUpdate: true }}
      style={{ height: height ?? '400px', width: width ?? '100%' }}
    />
  );
}
