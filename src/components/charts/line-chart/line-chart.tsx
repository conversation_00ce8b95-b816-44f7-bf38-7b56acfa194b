import { EChartsWrapper } from '@/components';
import { EChartsOption } from 'echarts';

export function LineChart() {
  const lineChartOptions: EChartsOption = {
    xAxis: {
      type: 'category',
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [150, 230, 224, 218, 135, 147, 260],
        type: 'line',
      },
    ],
  };

  return (
    <EChartsWrapper
      option={lineChartOptions}
      style={{ height: '400px', width: '100%' }}
    />
  );
}
