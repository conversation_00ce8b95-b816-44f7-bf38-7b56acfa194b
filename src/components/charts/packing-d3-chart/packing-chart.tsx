import { Card } from '@/components/card';
import { cn } from '@/lib/styles/utils';

type PackingChartProps = {
  tamValue: string;
  samValue: string;
  somValue: string;
  className?: string;
};

export function PackingChart({
  tamValue,
  somValue,
  samValue,
  className
}: PackingChartProps) {
  return (
    <Card className={cn('packing-wrapper w-[250px] h-[250px] flex items-center justify-center', className)}>
      <div className="w-[210px] h-[230px] bg-red-400 border-white border-[5px] rounded-t-[50%] rounded-b-[50%] rounded-l-[51%] rounded-r-[51%] relative">
        <span className="absolute top-5 left-1/2 transform -translate-x-1/2 font-bold text-white text-sm">
          {tamValue}
        </span>

        <div className="w-[150px] h-[170px] bg-blue-700 border-white border-[5px] rounded-t-[50%] rounded-b-[50%] rounded-l-[51%] rounded-r-[51%] absolute bottom-0 left-1/2 transform -translate-x-1/2">
          <span className="absolute top-8 left-1/2 transform -translate-x-1/2 font-bold text-white text-sm">
            {samValue}
          </span>
          <div className="w-[110px] h-[100px] bg-blue-300 border-white border-[5px] rounded-t-[50%] rounded-b-[50%] rounded-l-[51%] rounded-r-[51%] absolute bottom-0 left-1/2 transform -translate-x-1/2">
            <span className="absolute top-8 left-1/2 transform -translate-x-1/2 font-bold text-white text-sm">
              {somValue}
            </span>
          </div>
        </div>
      </div>
    </Card>
  );
}
