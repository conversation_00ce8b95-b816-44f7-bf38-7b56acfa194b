'use client';

import { ComponentProps, forwardRef } from 'react';
import { cn } from '@/lib/styles/utils';
import { useController, UseControllerProps } from 'react-hook-form';

type InputProps = ComponentProps<'input'> & UseControllerProps;

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type,
      name,
      rules,
      disabled,
      control,
      shouldUnregister,
      defaultValue,
      ...props
    },
    // eslint-disable-next-line
    _,
  ) => {
    const { field } = useController({
      name,
      rules,
      disabled,
      control,
      shouldUnregister,
      defaultValue,
    });

    return (
      <input
        type={type}
        className={cn(
          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
          className,
        )}
        {...field}
        {...props}
        value={field.value ?? ''}
      />
    );
  },
);

Input.displayName = 'Input';

export { Input };
