import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { refreshAccessToken } from '@/lib/config/nextauth.config';
import { clearStaleSession, isStaleToken } from '@/lib/utils/session-cleanup';

const secret = process.env.NEXTAUTH_SECRET;

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for auth routes to prevent infinite loops
  const isAuthRoute = pathname.startsWith('/login') || pathname.startsWith('/api/auth');
  if (isAuthRoute) {
    return NextResponse.next();
  }

  // Only apply middleware to protected routes
  const isProtectedRoute = pathname.startsWith('/main') || pathname === '/signup';

  if (!isProtectedRoute) {
    return NextResponse.next();
  }

  if (!secret) {
    console.error('NEXTAUTH_SECRET is not set');
    return NextResponse.redirect(new URL('/login', request.url));
  }

  try {
    // Get the current session token
    const token = await getToken({
      req: request,
      secret,
    });

    // If no token, redirect to login
    if (!token) {
      return NextResponse.redirect(new URL('/login', request.url));
    }

    // Check if token is stale/corrupted - clear aggressively
    if (isStaleToken(token)) {
      console.log('Stale token detected, clearing session');
      return clearStaleSession(request);
    }

    // Check if we should sign out (both tokens expired or invalid)
    if (shouldSignOut(token)) {
      console.log('Token expired, signing out');
      return clearStaleSession(request);
    }

    let response = NextResponse.next();

    // Set the current path header
    const fullPath = `${request.nextUrl.pathname}${request.nextUrl.search}`;
    response.headers.set('x-current-path', fullPath);

    // Check if token needs refresh
    if (shouldRefreshToken(token)) {
      console.log('Attempting token refresh');
      try {
        const refreshedToken = await refreshAccessToken(token);

        // If refresh failed, clear stale session
        if (refreshedToken.error) {
          console.log('Token refresh failed, clearing session');
          return clearStaleSession(request);
        }

        console.log('Token refreshed successfully');
        // Update the session cookie with refreshed token
        response = await updateSessionCookie(request, response, refreshedToken);
      } catch (error) {
        console.error('Error refreshing token in middleware:', error);
        return clearStaleSession(request);
      }
    }

    return response;
  } catch (error) {
    console.error('Middleware error:', error);
    return clearStaleSession(request);
  }
}

function shouldSignOut(token: any): boolean {
  // If no access token and no refresh token, sign out
  if (!token.accessToken && !token.refreshToken) {
    return true;
  }

  // If there's a refresh token error, sign out immediately
  if (token.error === 'RefreshAccessTokenError') {
    return true;
  }

  // If both tokens are expired, sign out
  const now = Date.now();
  const accessTokenExpired = !token.accessToken || now >= (token.accessTokenExpires || 0);
  const refreshTokenExpired = !token.refreshToken || (token.refreshTokenExpires && now >= token.refreshTokenExpires);

  return accessTokenExpired && refreshTokenExpired;
}

function shouldRefreshToken(token: any): boolean {
  // Don't try to refresh if there's already an error
  if (token.error === 'RefreshAccessTokenError') {
    return false;
  }

  // Don't try to refresh if no refresh token
  if (!token.refreshToken) {
    return false;
  }

  // Don't try to refresh if access token is not expired yet
  const now = Date.now();
  const accessTokenExpires = token.accessTokenExpires || 0;

  // Only refresh if access token is actually expired (with small buffer)
  const isExpired = now >= accessTokenExpires;

  if (!isExpired) {
    return false;
  }

  // Additional check: if access token is very old, don't try to refresh
  const tokenAge = now - (accessTokenExpires - (token.expiresIn || 3600) * 1000);
  const maxTokenAge = 24 * 60 * 60 * 1000; // 24 hours

  if (tokenAge > maxTokenAge) {
    console.log('Access token is too old, not attempting refresh');
    return false;
  }

  return true;
}

async function updateSessionCookie(
  request: NextRequest,
  response: NextResponse,
  refreshedToken: any
): Promise<NextResponse> {
  // Import encode function for creating new session token
  const { encode } = await import('next-auth/jwt');

  try {
    // Create new session token with refreshed data
    const newSessionToken = await encode({
      token: refreshedToken,
      secret: secret!,
    });

    // Update the session cookie
    const cookieName = request.cookies.get('next-auth.session-token')
      ? 'next-auth.session-token'
      : '__Secure-next-auth.session-token';

    response.cookies.set(cookieName, newSessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 30 * 24 * 60 * 60, // 30 days
    });

    return response;
  } catch (error) {
    console.error('Error updating session cookie:', error);
    throw error;
  }
}

export const config = {
  matcher: ['/main/:path*', '/signup'],
};
