import { withAuth } from 'next-auth/middleware';
import { NextRequest, NextResponse } from 'next/server';

export default withAuth(function middleware(request: NextRequest) {
  const response = NextResponse.next();
  const fullPath = `${request.nextUrl.pathname}${request.nextUrl.search}`;

  response.headers.set('x-current-path', fullPath);

  return response;
});

export const config = {
  matcher: ['/main/:path*', '/signup'],
};
