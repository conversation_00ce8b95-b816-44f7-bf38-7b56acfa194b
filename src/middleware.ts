import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { refreshAccessToken } from '@/lib/config/nextauth.config';
import { clearStaleSession, isStaleToken } from '@/lib/utils/session-cleanup';

const secret = process.env.NEXTAUTH_SECRET;

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Only apply middleware to protected routes
  const isProtectedRoute = pathname.startsWith('/main') || pathname === '/signup';

  if (!isProtectedRoute) {
    return NextResponse.next();
  }

  if (!secret) {
    console.error('NEXTAUTH_SECRET is not set');
    return NextResponse.redirect(new URL('/login', request.url));
  }

  try {
    // Get the current session token
    const token = await getToken({
      req: request,
      secret,
    });

    // If no token, redirect to login
    if (!token) {
      return NextResponse.redirect(new URL('/login', request.url));
    }

    // Check if token is stale/corrupted - clear aggressively
    if (isStaleToken(token)) {
      return clearStaleSession(request);
    }

    // Check if we should sign out (both tokens expired or invalid)
    if (shouldSignOut(token)) {
      return clearStaleSession(request);
    }

    let response = NextResponse.next();

    // Set the current path header
    const fullPath = `${request.nextUrl.pathname}${request.nextUrl.search}`;
    response.headers.set('x-current-path', fullPath);

    // Check if token needs refresh
    if (shouldRefreshToken(token)) {
      try {
        const refreshedToken = await refreshAccessToken(token);

        // If refresh failed, clear stale session
        if (refreshedToken.error) {
          return clearStaleSession(request);
        }

        // Update the session cookie with refreshed token
        response = await updateSessionCookie(request, response, refreshedToken);
      } catch (error) {
        console.error('Error refreshing token in middleware:', error);
        return clearStaleSession(request);
      }
    }

    return response;
  } catch (error) {
    console.error('Middleware error:', error);
    return NextResponse.redirect(new URL('/login', request.url));
  }
}

function shouldSignOut(token: any): boolean {
  // If no access token and no refresh token, sign out
  if (!token.accessToken && !token.refreshToken) {
    return true;
  }

  // If there's a refresh token error, sign out immediately
  if (token.error === 'RefreshAccessTokenError') {
    return true;
  }

  // If both tokens are expired, sign out
  const now = Date.now();
  const accessTokenExpired = !token.accessToken || now >= (token.accessTokenExpires || 0);
  const refreshTokenExpired = !token.refreshToken || (token.refreshTokenExpires && now >= token.refreshTokenExpires);

  return accessTokenExpired && refreshTokenExpired;
}

function shouldRefreshToken(token: any): boolean {
  // Don't try to refresh if there's already an error
  if (token.error === 'RefreshAccessTokenError') {
    return false;
  }

  // Don't try to refresh if no refresh token
  if (!token.refreshToken) {
    return false;
  }

  // If no access token or access token is expired, try to refresh
  const now = Date.now();
  return !token.accessToken || now >= (token.accessTokenExpires || 0);
}

async function updateSessionCookie(
  request: NextRequest,
  response: NextResponse,
  refreshedToken: any
): Promise<NextResponse> {
  // Import encode function for creating new session token
  const { encode } = await import('next-auth/jwt');

  try {
    // Create new session token with refreshed data
    const newSessionToken = await encode({
      token: refreshedToken,
      secret: secret!,
    });

    // Update the session cookie
    const cookieName = request.cookies.get('next-auth.session-token')
      ? 'next-auth.session-token'
      : '__Secure-next-auth.session-token';

    response.cookies.set(cookieName, newSessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 30 * 24 * 60 * 60, // 30 days
    });

    return response;
  } catch (error) {
    console.error('Error updating session cookie:', error);
    throw error;
  }
}

export const config = {
  matcher: ['/main/:path*', '/signup'],
};
