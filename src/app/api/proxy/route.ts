import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/config/nextauth.config';
import { NextRequest, NextResponse } from 'next/server';
import { config } from '@/lib/config';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.accessToken) {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { status: 401 }
      );
    }

    // Get the target URL from query parameters
    const { searchParams } = new URL(request.url);
    const targetPath = searchParams.get('path');
    
    if (!targetPath) {
      return NextResponse.json(
        { error: 'Missing path parameter' }, 
        { status: 400 }
      );
    }

    // Construct the full URL to your backend
    const backendUrl = `${config.api.base_url_for_server_side}${targetPath}`;
    
    // Add any additional query parameters
    const targetUrl = new URL(backendUrl);
    searchParams.forEach((value, key) => {
      if (key !== 'path') {
        targetUrl.searchParams.set(key, value);
      }
    });

    // Make the request to your backend with the fresh token
    const response = await fetch(targetUrl.toString(), {
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `Backend error: ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Proxy API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.accessToken) {
      return NextResponse.json(
        { error: 'Unauthorized' }, 
        { status: 401 }
      );
    }

    // Get the target URL from query parameters
    const { searchParams } = new URL(request.url);
    const targetPath = searchParams.get('path');
    
    if (!targetPath) {
      return NextResponse.json(
        { error: 'Missing path parameter' }, 
        { status: 400 }
      );
    }

    // Get request body
    const body = await request.json();

    // Construct the full URL to your backend
    const backendUrl = `${config.api.base_url_for_server_side}${targetPath}`;

    // Make the request to your backend with the fresh token
    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `Backend error: ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Proxy API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
