import { CSSProperties, ReactNode } from 'react';
import { SidebarProvider } from '@/components';
import MainSideBar from '@/app/(private)/(root)/main/_components/side-bar/main-side-bar';
import { PrivateHeader } from './_components/private-header';
import { PrivateHeaderProvider } from '@/app/(private)/(root)/_components/private-header/providers/private-header-provider';

export default async function PrivateRootLayout({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <div className="private-layout">
      <PrivateHeaderProvider>
        <div className="layout-wrapper">
          <SidebarProvider
            defaultOpen={false}
            style={
              {
                '--sidebar-width': '14rem',
                '--sidebar-width-icon': '81px',
              } as CSSProperties
            }
          >
            <MainSideBar />

            <div className="page-content w-[calc(100%_-_81px)]">
              <div className="w-full">
                <PrivateHeader />

                <div className="layout-content bg-[#F1F2F2] min-h-screen w-full">
                  {children}
                </div>
              </div>
            </div>
          </SidebarProvider>
        </div>
      </PrivateHeaderProvider>
    </div>
  );
}
