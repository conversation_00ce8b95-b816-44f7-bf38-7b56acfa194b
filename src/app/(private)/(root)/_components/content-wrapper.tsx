'use client';

import { ReactNode } from 'react';
import { useSidebar } from '@/components';
import { cn } from '@/lib/styles/utils';
import { usePathname } from 'next/navigation';

export function ContentWrapper({ children }: { children: ReactNode }) {
  const { isMobile, state } = useSidebar();
  const path = usePathname();

  if (!path.includes('/main/market/')) {
    return <div>{children}</div>;
  }
  return (
    <div
      className={cn('', {
        'w-full': isMobile,
        'w-[calc(100%-var(--sidebar-width))]':
          !isMobile && state === 'expanded',
        'w-[calc(100%-var(--sidebar-width-icon))]':
          !isMobile && state === 'collapsed',
      })}
    >
      {children}
    </div>
  );
}
