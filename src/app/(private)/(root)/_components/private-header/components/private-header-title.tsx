'use client';

import { usePrivateHeaderCtx } from '@/app/(private)/(root)/_components/private-header/providers/private-header-provider';
import { usePathname } from 'next/navigation';
import { useEffect } from 'react';
import { AddMarketToFavorites } from '@/app/(private)/(root)/_components/private-header/components/add-market-to-favorites';

export function PrivateHeaderTitle() {
  const ctx = usePrivateHeaderCtx();
  const pathname = usePathname();

  useEffect(() => {
    ctx.setTitleBarData({ title: '' });
  }, [pathname]); // eslint-disable-line

  return (
    <h1 className="text-2xl flex-1 relative text-center">
      {ctx.titleBar.title}

      {ctx.titleBar.pathToFav && (
        <AddMarketToFavorites className="ml-auto absolute right-6 top-1/2 transform -translate-y-1/2" pathToFav={ctx.titleBar.pathToFav} />
      )}
    </h1>
  );
}
