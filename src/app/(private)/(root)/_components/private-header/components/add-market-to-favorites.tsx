import { Button } from '@/components';
import { StarIcon } from 'lucide-react';
import { cn } from '@/lib/styles/utils';

export function AddMarketToFavorites({ className, pathToFav }: { className?: string, pathToFav?: string }) {
  return (
    <Button
      className={cn('', className)}
      onClick={() => {
        console.log(pathToFav);
      }}
      variant="hg_outline"
    >
      <StarIcon /> Add to Favorites
    </Button>
  );
}
