'use client';

import { createContext, ReactNode, useContext, useState } from 'react';

type TitleBar = {
  pathToFav?: string;
  title: string;
};

type PrivateHeaderCtxValue = {
  titleBar: TitleBar;
  setTitleBarData: (data: TitleBar) => void;
};

export const PrivateHeaderCtx = createContext<PrivateHeaderCtxValue | null>(
  null,
);

export function PrivateHeaderProvider({ children }: { children: ReactNode }) {
  const [titleBar, setTitleBarData] = useState<TitleBar>({title: ''});

  return (
    <PrivateHeaderCtx.Provider value={{ titleBar, setTitleBarData }}>
      {children}
    </PrivateHeaderCtx.Provider>
  );
}

export function usePrivateHeaderCtx() {
  const ctx = useContext(PrivateHeaderCtx);

  if (!ctx) {
    throw Error('Private header Ctx is needed');
  }

  return ctx;
}
