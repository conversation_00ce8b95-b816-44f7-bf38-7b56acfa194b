import { getServerSession } from 'next-auth';
import { ProfileDropdown } from './components/profile-dropdown';
import { Notifications } from './components/notifications';
import { redirect } from 'next/navigation';
import { PrivateHeaderTitle } from '@/app/(private)/(root)/_components/private-header/components/private-header-title';

export async function PrivateHeader() {
  const session = await getServerSession();

  if (!session) {
    redirect('/');
  }

  return (
    <header className="bg-white 300 h-[72px]">
      <nav className="h-full flex px-6 items-center justify-between">
        <div
          className="text-[#3769DA] text-xl leading-normal"
          style={{ fontFamily: 'var(--orbitron)' }}
        >
          HighGround
        </div>

        <PrivateHeaderTitle />

        <div className="user-panel flex">
          <Notifications />

          <div className="user-dropdown flex items-center ml-6 pl-6 border-l border-l-[#A9B0BB]">
            {session.user && session.user.name && (
              <ProfileDropdown name={session.user.name} />
            )}
          </div>
        </div>
      </nav>
    </header>
  );
}
