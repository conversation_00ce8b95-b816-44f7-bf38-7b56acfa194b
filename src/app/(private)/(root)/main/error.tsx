'use client';

export default function MainErrorPage({
  error,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const cause =
    error.name === 'TypeError'
      ? 'Something occurred while trying to access to a value'
      : '';

  return (
    <div className="p-10">
      <h1 className="text-xl mb-4">
        Something went wrong. Please contact the support.
      </h1>

      {!!cause && <p className="italic">{cause}</p>}
    </div>
  );
}
