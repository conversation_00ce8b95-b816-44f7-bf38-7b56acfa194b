'use client';

import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/card';
import { ScrollArea } from '@/components/scroll-area';
import { GOProgramListItem } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-program-list-item';
import { useSwrAuth } from '@/lib/hooks/use-swr-auth';
import { config } from '@/lib/config';
import { Skeleton } from '@/components';

export function GOProgramList({
  selectedServices,
  marketId,
}: {
  selectedServices: string[];
  marketId: string;
}) {
  const apiUrl = config.api.base_url;
  const path =
    selectedServices.length > 0
      ? `${apiUrl}/government/market/${marketId}/peos?services=${selectedServices}`
      : `${apiUrl}/government/market/${marketId}/peos`;
  const { data, isLoading } =
    useSwrAuth<{ id: string; name: string; totalValue: number }[]>(path);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-center">Funding Office</CardTitle>
      </CardHeader>

      <CardContent>
        <ScrollArea className="h-[253px]">
          {!isLoading ?
            <ul className="programs">
              {data &&
                data.map((program) => (
                  <GOProgramListItem
                    name={program.name}
                    key={program.id}
                  />
                ))}
            </ul> : <div className="w-full">
              <Skeleton className="w-full h-[40px] mb-2"/>
              <Skeleton className="w-full h-[40px] mb-2"/>
              <Skeleton className="w-full h-[40px] mb-2"/>
              <Skeleton className="w-full h-[40px] mb-2"/>
            </div>
          }
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
