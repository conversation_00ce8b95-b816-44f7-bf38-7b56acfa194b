'use client';

import { createContext, ReactNode, useContext, useState } from 'react';

type ToggleChartMode = {
  chartMode: boolean;
  toggle: () => void;
};

export const GOSeriesChartModeContext = createContext<
  ToggleChartMode | undefined
>(undefined);

export function GOSeriesContextWrapper({ children }: { children: ReactNode }) {
  const [chartMode, setChartMode] = useState(false);

  const toggle = () => setChartMode((prev) => !prev);

  return (
    <GOSeriesChartModeContext.Provider value={{ toggle, chartMode }}>
      {children}
    </GOSeriesChartModeContext.Provider>
  );
}

export function useChartMode() {
  const context = useContext(GOSeriesChartModeContext);
  if (!context) {
    throw new Error(
      'useChartMode should be inside of a GoSeriesContextWrapper',
    );
  }
  return context;
}
