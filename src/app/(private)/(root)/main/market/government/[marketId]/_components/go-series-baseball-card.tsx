import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from '@/components/sheet';
import { ReactNode } from 'react';
import { AwardData } from '@/app/(private)/(root)/main/market/government/[marketId]/_actions/filter-contracts';

export function GOSeriesBaseballCard({
  children,
  award,
}: {
  children: ReactNode;
  award: AwardData;
}) {
  return (
    <Sheet>
      <SheetTrigger
        asChild
        className="p-0"
      >
        <button className="p-0 text-sm block">{children}</button>
      </SheetTrigger>
      <SheetContent side="right_within">
        <SheetHeader>
          <SheetTitle className="mb-6">
            ContractId: {award.contractId}
          </SheetTitle>

          <ul className="pl-6 list-disc">
            <li className="mb-2">
              <b>Base and All Options Value:</b>{' '}
              {award.baseAndAllOptionsValue.toLocaleString('en-US', {
                style: 'currency',
                currency: 'USD',
                notation: 'compact',
              })}
            </li>

            <li className="mb-2">
              <b>Award Type:</b> {award.awardType}
            </li>

            <li className="mb-2">
              <b>Award Year:</b> {award.awardYear.split('T')[0]}
            </li>
          </ul>
        </SheetHeader>
      </SheetContent>
    </Sheet>
  );
}
