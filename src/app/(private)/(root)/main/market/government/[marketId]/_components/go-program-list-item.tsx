'use client';

import { Checkbox } from '@/components/forms/checkbox';
import { useEffect, useRef, useState } from 'react';
import { useSearchParamsManager } from '@/lib/hooks/use-update-search-params';

export function GOProgramListItem({ name }: { name: string }) {
  const ref = useRef<HTMLButtonElement>(null);
  const { getParam, updateListParam } = useSearchParamsManager();
  const [checked, setChecked] = useState<boolean>(false);

  useEffect(() => {
    let programList = ((getParam('programs') ?? '') as string).split(',');

    if (programList[0] === '') {
      programList = [];
    }

    setChecked(programList.includes(name));
  }, [getParam, name]);

  return (
    <li
      className="cursor-pointer border-b p-2 pb-3 select-none"
      onClick={() => {
        ref.current!.click();
      }}
    >
      <span className="flex items-center gap-x-1">
        <Checkbox
          ref={ref}
          checked={checked}
          onCheckedChange={() => {
            updateListParam('programs', name, 'toggle');
          }}
        />{' '}
        <span>{name}</span>
      </span>
    </li>
  );
}
