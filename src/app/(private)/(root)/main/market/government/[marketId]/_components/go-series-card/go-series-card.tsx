'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/card';
import { cn } from '@/lib/styles/utils';
import { GOSeriesBaseballCard } from '../go-series-baseball-card';
import { AwardData } from '@/app/(private)/(root)/main/market/government/[marketId]/_actions/filter-contracts';
import { ScrollArea } from '@/components/scroll-area';
import { Button } from '@/components';
import { SwitchCameraIcon } from 'lucide-react';
import { PieChart } from '@/components/charts/pie-chart';
import { groupBy } from 'lodash';
import {
  useChartMode
} from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-series-card/go-series.provider';
import { v4 as uuidv4 } from 'uuid';
import {
  newSeriesName
} from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/vc-series-card/vc-series-card';

type GOMarketSeriesCardProps = {
  contracts: AwardD<PERSON>[];
  seriesName: string;
  className?: string;
};

export function GOSeriesCard({
                               contracts,
                               seriesName,
                               className,
                             }: GOMarketSeriesCardProps) {
  const groupedByAwardType = groupBy(contracts, 'awardType');
  const mappedArray = Object.entries(groupedByAwardType).map(
    ([key, value]) => ({value: value.length, name: key}),
  );
  const {toggle, chartMode} = useChartMode();

  return (
    <div className={cn('series-wrapper', className)}>
      <Card className="flex-1 shrink-0 mb-4">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="font-bold">{newSeriesName(seriesName)}</span>

            <span>
              <Button
                onClick={() => toggle()}
                variant="ghost"
                size="sm"
              >
                <SwitchCameraIcon/>
              </Button>
            </span>
          </CardTitle>
        </CardHeader>

        <CardContent>
          <ScrollArea className="h-[320px] overflow-auto">
            {!chartMode ? (
              <ul>
                <li className="border-b border-t p-2 pb-3 bg-slate-200 font-bold">
                  Contracts
                </li>

                {contracts.map((award) => (
                  <li
                    key={uuidv4()}
                    className="border-b p-3 pb-3"
                  >
                    <GOSeriesBaseballCard award={award}>
                      <span>{award.contractId}</span>
                    </GOSeriesBaseballCard>
                  </li>
                ))}
              </ul>
            ) : (
              <PieChart data={mappedArray}/>
            )}
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}
