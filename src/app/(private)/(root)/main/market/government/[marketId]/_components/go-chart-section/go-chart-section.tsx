'use client';

import { SandChart } from '@/components/charts/sand-chart';
import { Card, CardContent } from '@/components/card';
import { SeriesOption } from 'echarts';
import { useSwrAuth } from '@/lib/hooks/use-swr-auth';
import { config } from '@/lib/config';

export function GOChartSection({
  programsParams,
  servicesParams,
  marketId,
}: {
  programsParams: string;
  servicesParams: string;
  marketId: string;
}) {
  const apiUrl = config.api.base_url;
  let url = `${apiUrl}/government/market/${marketId}/services/values`;

  if (!!programsParams && !servicesParams) {
    url = url + `?peos=${programsParams}`;
  }

  if (!!servicesParams && !programsParams) {
    url = url + `?services=${servicesParams}`;
  }

  if (!!servicesParams && !!programsParams) {
    url = url + `?services=${servicesParams}&peos=${programsParams}`;
  }

  const { data } = useSwrAuth<{ name: string; years: string[] }[]>(url);

  const legends =
    !!data && data.length > 0
      ? Object.keys(data[0].years)
      : ['2020', '2021', '2022', '2023', '2024', '2025', '2026', '2027']; // Placeholder

  const series =
    !!data && data.length > 0
      ? data.map((item) => {
          return {
            name: item.name,
            type: 'line',
            stack: 'Total',
            areaStyle: {},
            emphasis: {
              focus: 'series',
            },
            data: Object.values(item.years),
          } as SeriesOption;
        })
      : [];

  return (
    <div className="charts-section">
      <div className="sand-chard">
        <Card>
          <CardContent className="pt-6">
            <SandChart
              height="652px"
              series={series}
              programLegends={legends}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
