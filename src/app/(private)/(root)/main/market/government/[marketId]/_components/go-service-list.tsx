'use client';

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/card';
import { cn } from '@/lib/styles/utils';
import { GOServiceListItem } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-service-list-item';
import { useSwrAuth } from '@/lib/hooks/use-swr-auth';
import { config } from '@/lib/config';
import { Skeleton } from '@/components';
import { ScrollArea } from '@/components/scroll-area';

type GOServiceListProps = {
  className?: string;
  marketId: string;
};

export function GOServiceList({ marketId, className }: GOServiceListProps) {
  const apiUrl = config.api.base_url;

  const { data, isLoading } = useSwrAuth<
    { id: string; name: string; totalValue: number }[]
  >(`${apiUrl}/government/market/${marketId}/services`);

  return (
    <Card className={cn('w-[330px]', className)}>
      <CardHeader>
        <CardTitle className="text-center">Service Investment</CardTitle>
      </CardHeader>

      <CardContent>
        <ScrollArea className="h-[253px]">
          <ul className="[&>li+li]:mt-4">
            {!isLoading ? (
              data &&
              data.map(({ id, name, totalValue }) => (
                <GOServiceListItem
                  key={id}
                  name={name}
                  value={totalValue.toLocaleString('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    notation: 'compact',
                  })}
                />
              ))
            ) : (
              <div className="w-full">
                <Skeleton className="w-full h-[40px] mb-2" />
                <Skeleton className="w-full h-[40px] mb-2" />
                <Skeleton className="w-full h-[40px] mb-2" />
                <Skeleton className="w-full h-[40px] mb-2" />
              </div>
            )}
          </ul>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
