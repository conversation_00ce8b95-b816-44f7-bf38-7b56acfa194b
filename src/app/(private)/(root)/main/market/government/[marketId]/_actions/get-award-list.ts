'use server';

import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/config/nextauth.config';
import { AwardData } from './filter-contracts';
import { config } from '@/lib/config';
import { redirect } from 'next/navigation';

export async function getRealAwardList(
  id: string,
  programsParam?: string | undefined,
  servicesParams?: string | undefined,
): Promise<{
  marketTitle: string;
  marketDescription: string | null;
  contracts: AwardData[];
}> {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/');
  }

  const configBaseUrl = config.api.base_url_for_server_side;

  let url = `${configBaseUrl}/market/GOVERNMENT/${id}`;

  if (!!programsParam && !servicesParams) {
    url = url + `?peos=${programsParam}`;
  }

  if (!!servicesParams && !programsParam) {
    url = url + `?services=${servicesParams}`;
  }

  if (!!servicesParams && !!programsParam) {
    url = url + `?services=${servicesParams}&peos=${programsParam}`;
  }

  const request = await fetch(url, {
    headers: {
      Authorization: `Bearer ${session.accessToken}`,
    },
    // cache: 'force-cache'
  });

  return await request.json();
}
