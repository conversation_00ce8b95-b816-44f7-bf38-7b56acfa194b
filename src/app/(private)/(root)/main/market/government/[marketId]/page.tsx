import { MarketTitleSection } from '../../../_components/market-title-section';
import { GOServiceList } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-service-list';
import { GOChartSection } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-chart-section/go-chart-section';
import { GOProgramList } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-program-list';
import { getRealAwardList } from '@/app/(private)/(root)/main/market/government/[marketId]/_actions/get-award-list';
import { groupBy } from 'lodash';
import { GOSeriesCard } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-series-card/go-series-card';
import { Suspense } from 'react';
import GOChartSectionLoading from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-chart-section/loading';
import { GOSeriesContextWrapper } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-series-card/go-series.provider';
import { Metadata } from 'next';
import { PageParamsProps } from '@/lib/types/page-params-props.type';
import { headers } from 'next/headers';
import { v4 as uuidv4 } from 'uuid';

export const revalidate = 86400; // invalidate every 24 hours

export async function generateMetadata({
  params,
}: PageParamsProps<{ marketId: string }>): Promise<Metadata> {
  const finalParams = await params;
  const marketId = finalParams.marketId;
  const market = await getRealAwardList(marketId);

  return {
    title: `${market.marketTitle} | Government Dashboard | HighGround`,
    description: market.marketDescription,
  };
}

export default async function GovernmentOfficePage({
  params,
  searchParams,
}: {
  params: Promise<{ marketId: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const headersList = await headers();
  const path = headersList.get('x-current-path') ?? '';
  const finalSearchParams = await searchParams;
  const finalParams = await params;
  const marketId = finalParams.marketId;

  const servicesParam = !!finalSearchParams['services']
    ? (finalSearchParams['services'] as string).split(',')
    : [];

  const market = await getRealAwardList(
    marketId,
    finalSearchParams['programs'] as string,
    finalSearchParams['services'] as string,
  );

  const grouped = groupBy(market.contracts, 'series');
  const seriesItems = Object.entries(grouped).sort();

  return (
    <div className="market-page py-12 px-8 bg-[#F1F2F2]">
      <div className="wrapper">
        <div className="dashboard-header">
          <MarketTitleSection
            title={market.marketTitle}
            pathToFav={path}
          />
        </div>

        <div className="market-details mb-6 flex">
          <div className="top-pane w-full flex gap-x-6">
            <div className="left-side flex-shrink-0 flex-grow-0 basis-[330px] max-w-[330px] [&>div+div]:mt-4">
              <GOServiceList marketId={marketId} />

              <GOProgramList
                marketId={marketId}
                selectedServices={servicesParam}
              />
            </div>

            <section className="chart-section h-full flex-grow-0 flex-shrink-0 basis-[calc(100%_-_354px)]">
              <Suspense fallback={<GOChartSectionLoading />}>
                <GOChartSection
                  programsParams={finalSearchParams['programs'] as string}
                  marketId={marketId}
                  servicesParams={finalSearchParams['services'] as string}
                />
              </Suspense>
            </section>
          </div>
        </div>

        <div className="bottom-pane series-market flex gap-x-6 overflow-x-auto">
          <GOSeriesContextWrapper>
            {seriesItems.map(([key, value]) => (
              <GOSeriesCard
                className="flex-0 shrink-0 max-2xl:basis-[426px] max-2xl:max-w-[426px] 2xl:flex-1 2xl:shrink-1"
                contracts={value ?? []}
                seriesName={key}
                key={uuidv4()}
              />
            ))}
          </GOSeriesContextWrapper>
        </div>
      </div>
    </div>
  );
}
