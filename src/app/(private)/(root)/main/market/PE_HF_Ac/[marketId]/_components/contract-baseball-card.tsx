import {
  But<PERSON>,
  Sheet,
  She<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from '@/components';
import { ReactNode } from 'react';

type ContractBaseballCardProps = {
  children: ReactNode;
  contractTitle: string;
};

export function ContractBaseballCard({
  children,
  contractTitle,
}: ContractBaseballCardProps) {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button
          size="sm"
          className="whitespace-normal hover:underline hover:bg-transparent"
          variant="ghost"
        >
          {children}
        </Button>
      </SheetTrigger>
      <SheetContent side="right_within">
        <SheetHeader>
          <SheetTitle>{contractTitle}</SheetTitle>

          <SheetDescription>
            Lorem ipsum dolor sit amet, consectetur adipisicing elit.
            Accusantium corporis ducimus earum, est excepturi in laudantium
            nihil possimus praesentium quae quo, saepe soluta totam unde vel!
            Architecto cumque ex perferendis?
          </SheetDescription>
        </SheetHeader>
      </SheetContent>
    </Sheet>
  );
}
