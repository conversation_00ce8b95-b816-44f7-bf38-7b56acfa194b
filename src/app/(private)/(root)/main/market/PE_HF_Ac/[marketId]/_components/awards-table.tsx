import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/table';
import { cn } from '@/lib/styles/utils';
import { Badge } from '@/components/badge';
import { ContractBaseballCard } from './contract-baseball-card';
import { TargetCompaniesBaseballCard } from './target-companies-baseball-card';
import { ContractContextMenu } from './contract-context-menu';
import { TargetCompanyContextMenu } from './target-company-context-menu';
import { PE_HF_AcAward } from '@/app/(private)/(root)/main/market/_lib/types/market-segment.type';

type ContractsTableProps = {
  contracts: PE_HF_AcAward[];
  className?: string;
};

export function AwardsTable({ contracts, className }: ContractsTableProps) {
  return (
    <div className={cn('table-wrapper max-w-[900px]', className)}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Contract Data</TableHead>

            <TableHead>Key Data Indicators</TableHead>

            <TableHead>Target Companies</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          {contracts.map((contract) => {
            return (
              <TableRow key={contract.id}>
                <TableCell className="font-medium">
                  <ContractBaseballCard
                    contractTitle={contract.opportunityTitle}
                  >
                    <ContractContextMenu>
                      {contract.opportunityTitle}
                    </ContractContextMenu>
                  </ContractBaseballCard>
                </TableCell>

                <TableCell>
                  <span className="flex flex-wrap gap-y-2">
                    {contract.keyDataIndicators.map((item) => {
                      return (
                        <Badge
                          variant="outline"
                          className="ml-1 bg-amber-200 text-black"
                          key={item}
                        >
                          {item}
                        </Badge>
                      );
                    })}
                  </span>
                </TableCell>

                <TableCell>
                  <span className="flex flex-wrap gap-2">
                    {contract.industryEntities.map((target) => {
                      return (
                        <TargetCompaniesBaseballCard
                          key={target.id}
                          companyName={target.legalBusinessName}
                        >
                          <TargetCompanyContextMenu>
                            {target.legalBusinessName}
                          </TargetCompanyContextMenu>
                        </TargetCompaniesBaseballCard>
                      );
                    })}
                  </span>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
