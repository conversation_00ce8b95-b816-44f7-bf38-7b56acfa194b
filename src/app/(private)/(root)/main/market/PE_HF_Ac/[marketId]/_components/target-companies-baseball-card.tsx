import {
  But<PERSON>,
  Sheet,
  She<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from '@/components';
import { ReactNode } from 'react';

type TargetCompaniesBaseballCardProps = {
  children: ReactNode;
  companyName: string;
};

export function TargetCompaniesBaseballCard({
  children,
  companyName,
}: TargetCompaniesBaseballCardProps) {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button
          variant="outline"
          className="p-2"
          size="sm"
        >
          {children}
        </Button>
      </SheetTrigger>
      <SheetContent side="right_within">
        <SheetHeader>
          <SheetTitle>{companyName}</SheetTitle>

          <SheetDescription>
            Lorem ipsum dolor sit amet, consectetur adipisicing elit.
            Accusantium corporis ducimus earum, est excepturi in laudantium
            nihil possimus praesentium quae quo, saepe soluta totam unde vel!
            Architecto cumque ex perferendis?
          </SheetDescription>
        </SheetHeader>
      </SheetContent>
    </Sheet>
  );
}
