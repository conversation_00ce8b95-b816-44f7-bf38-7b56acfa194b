import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/config/nextauth.config';
import { redirect } from 'next/navigation';
import { config } from '@/lib/config';
import { PE_HF_AcMarket } from '@/app/(private)/(root)/main/market/_lib/types/market-segment.type';

export async function getHFDashboardDetails(
  marketId: string,
): Promise<PE_HF_AcMarket | null> {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/');
  }

  const configBaseUrl = config.api.base_url_for_server_side;
  const url = `${configBaseUrl}/market/HF_PE_Ac/${marketId}`;

  const request = await fetch(url, {
    headers: {
      Authorization: `Bearer ${(session as any)['accessToken']}`, // eslint-disable-line
    },
  });

  return await request.json();
}
