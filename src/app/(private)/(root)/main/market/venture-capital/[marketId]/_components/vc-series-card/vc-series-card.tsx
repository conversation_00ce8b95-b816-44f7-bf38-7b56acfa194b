import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/card';
import { MarketSize } from '@/app/(private)/(root)/main/market/_lib/types/market-segment.type';
import { PackingChart } from '@/components/charts/packing-d3-chart';
import { VcSeriesContextMenu } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/vc-series-context-menu';
import { VcSeriesBaseballCard } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/vc-series-baseball-card';
import { cn } from '@/lib/styles/utils';
import { BuildingIcon, ScrollTextIcon, UserSearchIcon } from 'lucide-react';
import { VCSeriesFilterButton } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/vc-series-card/vc-series-filter-button';
import { ScrollArea } from '@/components/scroll-area';
import { VCSeriesCardTitle } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/vc-series-card/vc-series-card-title';

export type VentureMarketFilteredSeries = {
  id: string;
  name: string;
  marketSize: MarketSize;
  data: {
    id: string;
    name: string;
  }[];
};

type VentureMarketSeriesCardProps = {
  marketSeries: VentureMarketFilteredSeries;
  className?: string;
};

export function newSeriesName(seriesName: string): string {
  if (seriesName.includes('Series A')) {
    return 'Early Stage';
  }

  if (seriesName.includes('Series B')) {
    return 'Mid Stage';
  }

  if (seriesName.includes('Series C')) {
    return 'Growth';
  }

  if (seriesName.includes('Series D')) {
    return 'Enterpise';
  }

  return seriesName;
}

export function VcSeriesCard({
  marketSeries,
  className,
}: VentureMarketSeriesCardProps) {
  return (
    <div className={cn('series-wrapper', className)}>
      <Card className="flex-1 shrink-0 mb-4">
        <CardHeader className="pb-0">
          <CardTitle className="flex items-center">
            <span>{newSeriesName(marketSeries.name)}</span>

            <span className="actions ml-auto [&>button+button]:ml-2">
              <VCSeriesFilterButton
                title="Companies"
                filter="COMPANIES"
              >
                <BuildingIcon />
              </VCSeriesFilterButton>

              <VCSeriesFilterButton
                title="Contracts"
                filter="CONTRACTS"
              >
                <ScrollTextIcon />
              </VCSeriesFilterButton>

              <VCSeriesFilterButton
                title="Customers"
                filter="CUSTOMERS"
              >
                <UserSearchIcon />
              </VCSeriesFilterButton>
            </span>
          </CardTitle>
        </CardHeader>

        <CardContent className="pt-2">
          <ul>
            <li className="border-b border-t p-2 pb-3 bg-slate-200 font-bold">
              {<VCSeriesCardTitle />}
            </li>

            <ScrollArea className="h-[226px] overflow-auto">
              {marketSeries.data.map((item) => (
                <li
                  key={item.id}
                  className="border-b p-2 pb-3"
                >
                  <VcSeriesContextMenu>
                    <VcSeriesBaseballCard itemName={item.name}>
                      <span>{item.name}</span>
                    </VcSeriesBaseballCard>
                  </VcSeriesContextMenu>
                </li>
              ))}
            </ScrollArea>
          </ul>
        </CardContent>
      </Card>

      <PackingChart
        className="flex-1 shrink-0 mx-auto"
        tamValue={marketSeries.marketSize.tam.toLocaleString('en-US', {
          style: 'currency',
          currency: 'USD',
        })}
        samValue={marketSeries.marketSize.sam.toLocaleString('en-US', {
          style: 'currency',
          currency: 'USD',
        })}
        somValue={marketSeries.marketSize.som.toLocaleString('en-US', {
          style: 'currency',
          currency: 'USD',
        })}
      />
    </div>
  );
}
