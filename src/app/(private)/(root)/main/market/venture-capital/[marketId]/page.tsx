import { notFound } from 'next/navigation';
import { VcChartSection } from './_components/vc-chart-section';
import { MarketTitleSection } from '@/app/(private)/(root)/main/_components/market-title-section/market-title-section';
import {
  VcSeriesCard,
  VentureMarketFilteredSeries,
} from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/vc-series-card/vc-series-card';
import { IndicatorCard } from '@/app/(private)/(root)/main/home/<USER>/indicator-card';
import { VcServicesBreakdown } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/vc-services-breakdown';
import { getVentureCapitalDashboardDetails } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_actions/get-vc-dashboard-details';
import { PageParamsProps } from '@/lib/types/page-params-props.type';
import { Metadata } from 'next';
import { Separator } from '@/components/separator';
import { headers } from 'next/headers';
import { getVcDashboardSeries } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_actions/get-vc-dashboard-series';
import { getVcDashboardChart } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_actions/get-vc-dashboard-chart';
import { VCSeriesProvider } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/vc-series-card/vc-series-provider';
import { PageSearchParams } from '@/lib/types/page-search-params';

export const revalidate = 86400; // invalidate every 24 hours

export async function generateMetadata({
  params,
}: PageParamsProps<{ marketId: string }>): Promise<Metadata> {
  const finalParams = await params;
  const marketId = finalParams.marketId;
  const market = await getVentureCapitalDashboardDetails(marketId);

  return {
    title: `${market ? market.title + ' | ' : ''} Venture Capital Dashboard | HighGround`,
    description: market?.details.description,
  };
}

export default async function ContractIdPage({
  params,
  searchParams,
}: {
  params: Promise<{ marketId: string }>;
  searchParams: PageSearchParams;
}) {
  const wSearchParams = await searchParams;
  const headersList = await headers();
  const path = headersList.get('x-current-path') ?? '';

  const { marketId } = await params;
  const currentMarket = await getVentureCapitalDashboardDetails(marketId);
  const marketSeries: VentureMarketFilteredSeries[] =
    await getVcDashboardSeries(marketId, wSearchParams['series'] as string);

  const charData: {
    name: string;
    years: Record<string, number>;
  }[] = await getVcDashboardChart(marketId, wSearchParams['series'] as string);

  if (!currentMarket) {
    notFound();
  }

  return (
    <div className="market-page py-12 px-8 bg-[#F1F2F2]">
      <div className="wrapper">
        <MarketTitleSection
          title={currentMarket.title}
          pathToFav={path}
        />

        <div className="flex">
          <div className="left-pane-venture-market max-w-[255px] flex-grow-0 flex-shrink-0 basis-[255px] [&>div+div]:mt-6">
            <IndicatorCard
              name="Total Addressable Market"
              indicator={currentMarket.details.totalSpend.toLocaleString(
                'en-US',
                {
                  style: 'currency',
                  currency: 'USD',
                  notation: 'compact',
                },
              )}
            />

            <IndicatorCard
              name="Customer Total"
              indicator={currentMarket.details.customerTotal + ''}
              color="secondary"
            />

            <VcServicesBreakdown
              services={currentMarket.details.servicesBreakdown}
            />
          </div>

          <div className="venture-market pl-8 flex-grow-0 flex-shrink-0 basis-[calc(100%_-_255px)] max-w-[calc(100%_-_255px)] h-[624px]">
            <VcChartSection chartData={charData} />
          </div>
        </div>

        <Separator className="my-6" />

        <VCSeriesProvider>
          <div className="series flex gap-x-4 justify-between max-w-full w-full overflow-x-auto">
            {marketSeries.map((series) => (
              <VcSeriesCard
                className="flex-0 shrink-0 max-2xl:basis-[426px] max-2xl:max-w-[426px] 2xl:flex-1 2xl:shrink-1"
                marketSeries={series}
                key={series.id}
              />
            ))}
          </div>
        </VCSeriesProvider>
      </div>
    </div>
  );
}
