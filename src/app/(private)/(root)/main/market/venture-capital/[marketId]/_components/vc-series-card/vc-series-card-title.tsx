'use client';

import {
  useVCSeriesContext, VCSeriesFilter
} from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/vc-series-card/vc-series-provider';

function seriesTitle(filter: VCSeriesFilter) {
  switch (filter) {
    case 'COMPANIES':
      return 'Companies';
    case 'CONTRACTS':
      return 'Contracts';
    case 'CUSTOMERS':
      return 'Customers';
  }
}

export function VCSeriesCardTitle() {
  const {seriesFilter} = useVCSeriesContext();

  return <span>{seriesTitle(seriesFilter)}</span>;
}