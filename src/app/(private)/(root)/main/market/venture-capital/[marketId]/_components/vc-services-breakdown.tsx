import { cn } from '@/lib/styles/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/card';
import { ScrollArea } from '@/components/scroll-area';

type VcServiceBreakdownProps = {
  className?: string;
  services: Record<string, number>;
};

export function VcServicesBreakdown({
  className,
  services
}: VcServiceBreakdownProps) {
  return (
    <div className={cn('service-breakdown', className)}>
      <Card className="flex-1 shrink-0">
        <CardHeader className="pb-0">
          <CardTitle>Sub-Market Breakdown</CardTitle>
        </CardHeader>

        <CardContent className="pt-2">
          <ScrollArea className="h-[226px] overflow-auto">
            <ul>
              <li className="border-b border-t p-2 pb-3 bg-slate-200 font-bold">
                Title
              </li>

              {Object.entries(services ?? {}).map((item) => (
                <li
                  className="border-b p-2 pb-3"
                  key={item[0]}
                >
                  <span>
                    {item[0]} ({item[1]}%)
                  </span>
                </li>
              ))}
            </ul>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}
