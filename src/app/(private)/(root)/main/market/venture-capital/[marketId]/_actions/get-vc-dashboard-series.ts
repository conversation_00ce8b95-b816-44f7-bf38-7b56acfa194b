'use server';

import { redirect } from 'next/navigation';
import { config } from '@/lib/config';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/config/nextauth.config';

export async function getVcDashboardSeries(marketId: string, filter: string | undefined) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/');
  }

  let finalFilter = ''

  if ((filter !== 'CONTRACTS' && filter !== 'COMPANIES' && filter !== 'CUSTOMERS') || filter === undefined) {
    finalFilter = 'COMPANIES';
  } else {
    finalFilter = filter;
  }

  const configBaseUrl = config.api.base_url_for_server_side;
  const url = `${configBaseUrl}/venture-capital/market/${marketId}/series?series=${finalFilter}`;

  const request = await fetch(url, {
    headers: {
      Authorization: `Bearer ${session.accessToken}`,
    },
  });

  return await request.json();
}
