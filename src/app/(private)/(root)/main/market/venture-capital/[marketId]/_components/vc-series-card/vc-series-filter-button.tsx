'use client';

import { Button } from '@/components/button';
import { cn } from '@/lib/styles/utils';
import {
  useVCSeriesContext,
  VCSeriesFilter,
} from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/vc-series-card/vc-series-provider';
import { ReactNode } from 'react';
import { useRouter } from 'next/navigation';

export function VCSeriesFilterButton({
  children,
  filter,
  title,
}: {
  children: ReactNode;
  filter: VCSeriesFilter;
  title: string;
}) {
  const { setSeriesFilter, seriesFilter } = useVCSeriesContext();
  const router = useRouter();

  return (
    <Button
      variant="hg_outline"
      size="icon"
      title={title}
      onClick={() => {
        setSeriesFilter(filter);
        router.push(`?series=${filter}`);
      }}
      className={cn(seriesFilter === filter && 'bg-blue-100')}
    >
      {children}
    </Button>
  );
}
