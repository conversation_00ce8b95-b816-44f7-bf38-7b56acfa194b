import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  Sheet<PERSON>eader,
  Sheet<PERSON>itle,
  Sheet<PERSON>rigger,
} from '@/components/sheet';
import { ReactNode } from 'react';

export function VcSeriesBaseballCard({
  children,
  itemName,
}: {
  children: ReactNode;
  itemName: string;
}) {
  return (
    <Sheet>
      <SheetTrigger
        asChild
        className="p-0"
      >
        <button className="p-0 text-sm block">{children}</button>
      </SheetTrigger>
      <SheetContent side="right_within">
        <SheetHeader>
          <SheetTitle>{itemName}</SheetTitle>

          <SheetDescription>
            Lorem ipsum dolor sit amet, consectetur adipisicing elit.
            Accusantium corporis ducimus earum, est excepturi in laudantium
            nihil possimus praesentium quae quo, saepe soluta totam unde vel!
            Architecto cumque ex perferendis?
          </SheetDescription>
        </SheetHeader>
      </SheetContent>
    </Sheet>
  );
}
