import { Sand<PERSON>hart } from '@/components/charts/sand-chart';
import { Card, CardContent } from '@/components/card';
import { SeriesOption } from 'echarts';

export type VCChartRecord = {
  name: string;
  years: Record<string, number>;
};

export function VcChartSection({ chartData }: { chartData: VCChartRecord[] }) {
  const legends = ['2021', '2022', '2023', '2024', '2025'];

  const series = chartData.map((item) => {
    return {
      name: item.name,
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: {
        focus: 'series',
      },
      data: Object.values(item.years),
    } as SeriesOption;
  });

  return (
    <div className="sand-chard h-full">
      <Card className="h-full">
        <CardContent className="pt-6 h-full">
          <SandChart
            series={series}
            programLegends={legends}
            height="100%"
          />
        </CardContent>
      </Card>
    </div>
  );
}
