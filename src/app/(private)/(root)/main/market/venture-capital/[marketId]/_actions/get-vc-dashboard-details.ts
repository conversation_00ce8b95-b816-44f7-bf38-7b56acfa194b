'use server';

import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/config/nextauth.config';
import { redirect } from 'next/navigation';
import { config } from '@/lib/config';
import { VentureCapitalMarket } from '@/app/(private)/(root)/main/market/_lib/types/market-segment.type';

export async function getVentureCapitalDashboardDetails(
  marketId: string,
): Promise<VentureCapitalMarket | null> {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/');
  }

  const configBaseUrl = config.api.base_url_for_server_side;
  const url = `${configBaseUrl}/market/VENTURE_CAPITAL/${marketId}`;

  const request = await fetch(url, {
    headers: {
      Authorization: `Bearer ${session.accessToken}`,
    },
    cache: 'force-cache'
  });

  return await request.json();
}
