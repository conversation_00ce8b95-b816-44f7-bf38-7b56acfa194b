'use client';

import { createContext, ReactNode, useContext, useState } from 'react';
import { useSearchParams } from 'next/navigation';

export type VCSeriesFilter = 'CONTRACTS' | 'CUSTOMERS' | 'COMPANIES';

type VCSeriesContextValue = {
  seriesFilter: VCSeriesFilter;
  setSeriesFilter: (filter: VCSeriesFilter) => void;
};

export const VCSeriesContext = createContext<VCSeriesContextValue | null>(null);

export function VCSeriesProvider({ children }: { children: ReactNode }) {
  const searchParams = useSearchParams();
  const filterToApply = (searchParams.get('series') ??
    'COMPANIES') as VCSeriesFilter;
  const [seriesFilter, setSeriesFilter] =
    useState<VCSeriesFilter>(filterToApply);

  return (
    <VCSeriesContext.Provider value={{ setSeriesFilter, seriesFilter }}>
      {children}
    </VCSeriesContext.Provider>
  );
}

export function useVCSeriesContext() {
  const ctx = useContext(VCSeriesContext);

  if (!ctx) {
    throw Error('VCSeries provider is needed');
  }

  return ctx;
}
