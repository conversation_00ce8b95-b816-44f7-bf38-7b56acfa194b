import { Button } from '@/components/button';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/sheet';
import { ReactNode } from 'react';

export function SeriesBaseballCard({
  children,
  contractTitle,
}: {
  children: ReactNode;
  contractTitle: string;
}) {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          className="font-normal"
        >
          {children}
        </Button>
      </SheetTrigger>
      <SheetContent side="right_within">
        <SheetHeader>
          <SheetTitle>{contractTitle}</SheetTitle>

          <SheetDescription>
            Lorem ipsum dolor sit amet, consectetur adipisicing elit.
            Accusantium corporis ducimus earum, est excepturi in laudantium
            nihil possimus praesentium quae quo, saepe soluta totam unde vel!
            Architecto cumque ex perferendis?
          </SheetDescription>
        </SheetHeader>
      </SheetContent>
    </Sheet>
  );
}
