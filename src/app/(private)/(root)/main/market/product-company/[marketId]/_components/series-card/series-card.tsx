import { Card, CardContent } from '@/components/card';
import { cn } from '@/lib/styles/utils';
import { SeriesBaseballCard } from '@/app/(private)/(root)/main/market/product-company/[marketId]/_components/series-card/series-baseball-card';
import { ProductCompanySeriesContextMenu } from '@/app/(private)/(root)/main/market/product-company/[marketId]/_components/series-card/product-company-series-context-menu';
import { ProductCompanyInvestmentSeries } from '@/app/(private)/(root)/main/market/_lib/types/market-segment.type';

type SeriesCardProps = {
  series: ProductCompanyInvestmentSeries;
  className?: string;
};

export function SeriesCard({ series, className }: SeriesCardProps) {
  return (
    <div className={cn('series-container max-w-sm', className)}>
      <h3 className="text-center mb-4 text-lg">
        <div>{series.name}</div>
      </h3>

      <Card>
        <CardContent className="pt-6">
          <ul className="[&>li+li]:mt-4">
            {series.awards.map((item) => (
              <li key={item.id}>
                <ProductCompanySeriesContextMenu>
                  <SeriesBaseballCard contractTitle={item.opportunityTitle}>
                    {item.opportunityTitle}
                  </SeriesBaseballCard>
                </ProductCompanySeriesContextMenu>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
