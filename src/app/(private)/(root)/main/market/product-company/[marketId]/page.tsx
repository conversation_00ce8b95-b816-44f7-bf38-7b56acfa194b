import { notFound } from 'next/navigation';
import { MarketTitleSection } from '../../../_components/market-title-section';
import { SeriesCard } from '@/app/(private)/(root)/main/market/product-company/[marketId]/_components/series-card/series-card';
import { getPCDashboardDetails } from '@/app/(private)/(root)/main/market/product-company/[marketId]/_actions/get-pc-dashboard-details';
import { PageParamsProps } from '@/lib/types/page-params-props.type';
import { Metadata } from 'next';
import { headers } from 'next/headers';

export const revalidate = 86400; // invalidate every 24 hours

export async function generateMetadata({
  params,
}: PageParamsProps<{ marketId: string }>): Promise<Metadata> {
  const finalParams = await params;
  const marketId = finalParams.marketId;
  const market = await getPCDashboardDetails(marketId);

  return {
    title: `${market ? market.title + ' | ' : ''} Product Company Dashboard | HighGround`,
  };
}

export default async function ContractIdPage({
  params,
}: {
  params: Promise<{ marketId: string }>;
}) {
  const headersList = await headers();
  const path = headersList.get('x-current-path') ?? '';
  const { marketId } = await params;

  const currentContract = await getPCDashboardDetails(marketId);

  if (!currentContract) {
    notFound();
  }

  return (
    <div className="product-capital-page py-16 px-8">
      <div className="wrapper wrapper pt-8">
        <MarketTitleSection title={currentContract.title} pathToFav={path}/>

        <div className="page-content">
          <div className="series-container flex gap-x-4">
            {currentContract.productCompanyInvestmentSeries.map((item) => (
              <SeriesCard
                series={item}
                className="flex-grow-0 flex-shrink basis-1/4 max-w-[25%]"
                key={item.id}
              />
            ))}
          </div>

          <div className="calendar-container"></div>
        </div>
      </div>
    </div>
  );
}
