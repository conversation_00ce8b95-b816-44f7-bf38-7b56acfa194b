import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from '@/components/context-menu';
import { ReactNode } from 'react';

export function ProductCompanySeriesContextMenu({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <ContextMenu>
      <ContextMenuTrigger>{children}</ContextMenuTrigger>
      <ContextMenuContent>
        <ContextMenuItem>Add to Watchlist</ContextMenuItem>

        <ContextMenuItem>Add to Pipeline</ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  );
}
