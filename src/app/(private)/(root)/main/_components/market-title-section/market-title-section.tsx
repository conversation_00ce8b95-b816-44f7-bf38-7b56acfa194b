'use client';

import { usePrivateHeaderCtx } from '@/app/(private)/(root)/_components/private-header/providers/private-header-provider';
import { useEffect } from 'react';

export function MarketTitleSection({ title, pathToFav }: { title: string, pathToFav?: string }) {
  const ctx = usePrivateHeaderCtx();


  useEffect(() => {
    ctx.setTitleBarData({title, pathToFav});
  }, [title, pathToFav]); // eslint-disable-line

  return <></>;
}
