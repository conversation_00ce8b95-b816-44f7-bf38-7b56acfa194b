import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components';
import Link from 'next/link';
import { HomeIcon, RssIcon, StarIcon } from 'lucide-react';
import { cn } from '@/lib/styles/utils';
import { HighGroundLogo } from '@/components/icons/high-ground-logo';
import { CSSProperties } from 'react';

type MainSideBarProps = {
  className?: string;
};

export default function MainSideBar({ className }: MainSideBarProps) {
  return (
    <aside className={cn('', className)}>
      <Sidebar
        style={
          {
            '--sidebar-background': '221.6deg 68.78% 53.53%',
          } as CSSProperties
        }
        collapsible="icon"
      >
        <SidebarHeader className="mb-12 pt-6">
          <SidebarMenu>
            <SidebarMenuItem className="flex justify-center">
              <SidebarMenuButton
                asChild
                className="[&>svg]:size-8 group-data-[collapsible=icon]:[&>svg]:-ml-2"
              >
                <Link
                  href="/main/home?market=VENTURE_CAPITAL&search="
                  className="hover:!bg-transparent"
                >
                  <HighGroundLogo />
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>

        <SidebarContent>
          <SidebarGroup className="pt-6">
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem className="flex justify-center">
                  <SidebarMenuButton
                    asChild
                    className="[&>svg]:size-6 group-data-[collapsible=icon]:[&>svg]:-ml-1"
                  >
                    <Link
                      href="/main/home?market=VENTURE_CAPITAL"
                      title="Home"
                      className="h-[24px]"
                    >
                      <HomeIcon size={24} />

                      <span className="text-lg">Home</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>

                <SidebarMenuItem className="flex justify-center">
                  <SidebarMenuButton
                    asChild
                    className="[&>svg]:size-6 group-data-[collapsible=icon]:[&>svg]:-ml-1"
                  >
                    <Link
                      href="/main/watchlist"
                      title="Watchlist"
                    >
                      <RssIcon />

                      <span className="text-lg">Watchlist</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>

                <SidebarMenuItem className="flex justify-center">
                  <SidebarMenuButton
                    asChild
                    className="[&>svg]:size-6 group-data-[collapsible=icon]:[&>svg]:-ml-1"
                  >
                    <Link
                      href="/main/favorites"
                      title="Favorites"
                    >
                      <StarIcon />

                      <span className="text-lg">Favorites</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
      </Sidebar>
    </aside>
  );
}
