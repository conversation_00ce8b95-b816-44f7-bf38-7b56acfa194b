'use client';

import { ArrowLeftToLineIcon, ArrowRightToLineIcon } from 'lucide-react';
import { SidebarMenuButton, useSidebar } from '@/components/side-bar';

export default function MainSideBarButtonState() {
  const { state, setOpen } = useSidebar();

  return (
    <SidebarMenuButton
      onClick={() => setOpen(state === 'collapsed')}
      title={`${state === 'expanded' ? 'Collapse' : 'Expand'} - Ctrl + b`}
      className="flex justify-end [&>svg]:size-6 group-data-[collapsible=icon]:[&>svg]:-ml-1"
    >
      {state === 'expanded' ? (
        <ArrowLeftToLineIcon className="inline-block" />
      ) : (
        <ArrowRightToLineIcon className="inline-block" />
      )}
    </SidebarMenuButton>
  );
}
