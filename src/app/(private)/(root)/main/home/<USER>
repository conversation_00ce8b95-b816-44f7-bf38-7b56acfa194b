import { SearchForm } from './_components/search-form';
import { Metadata } from 'next';
import { IndicatorCard } from '@/app/(private)/(root)/main/home/<USER>/indicator-card';
import { getHomeIndicatorsData } from '@/app/(private)/(root)/main/home/<USER>/get-home-indicators-data';

export const metadata: Metadata = {
  title: 'Home | HighGround',
};

export default async function HomePage() {
  const homeIndicators = await getHomeIndicatorsData();

  return (
    <div className="home-page">
      <div className="page-wrapper py-16">
        <div className="page-content">
          <div className="info-wrapper max-w-2xl mx-auto">
            <div className="search-form-wrapper relative w-full h-12 mb-4">
              <SearchForm className="mb-2 absolute w-full" />
            </div>

            <div className="indicators">
              <div className="indicators-row flex gap-x-4 mb-4">
                <IndicatorCard
                  className="max-w-auto flex-0 shrink-1 basis-1/3 max-w-[33.3333333%]"
                  name="Markets"
                  indicator={homeIndicators.markets.toLocaleString('en-US', {
                    notation: 'compact',
                  })}
                />

                <IndicatorCard
                  className="flex-0 shrink-1 basis-1/3 max-w-[33.3333333%]"
                  name="Customers"
                  color="secondary"
                  indicator={homeIndicators.customers.toLocaleString('en-US', {
                    notation: 'compact',
                  })}
                />

                <IndicatorCard
                  className="flex-0 shrink-1 basis-1/3 max-w-[33.3333333%]"
                  name="Companies"
                  indicator={homeIndicators.companies.toLocaleString('en-US', {
                    notation: 'compact',
                  })}
                />
              </div>

              <div className="indicator-row flex gap-x-4 mb-4">
                <IndicatorCard
                  className="flex-0 shrink-1 basis-1/2 max-w-[50%]"
                  name="Contracts"
                  indicator={homeIndicators.contracts.toLocaleString('en-US', {
                    notation: 'compact',
                  })}
                />

                <IndicatorCard
                  className="flex-0 shrink-1 basis-1/2 max-w-[50%]"
                  name="Opportunities"
                  color="secondary"
                  indicator={homeIndicators.opportunities.toLocaleString(
                    'en-US',
                    {
                      notation: 'compact',
                    },
                  )}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
