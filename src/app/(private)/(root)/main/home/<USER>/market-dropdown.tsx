import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/dropdown-menu';
import { ComponentProps, ReactNode } from 'react';
import { Button } from '@/components';
import { CheckIcon, ChevronDown } from 'lucide-react';
import { useController, UseControllerProps } from 'react-hook-form';
import { MarketValue } from '@/app/(private)/(root)/main/market/_lib/types/market.enum';
import { Market } from '@/app/(private)/(root)/main/market/_lib/types/market-segment.type';

type MarketDropdownProps = {
  children?: ReactNode;
} & ComponentProps<'button'> &
  UseControllerProps;

export const MARKETS: Market[] = [
  {
    label: 'Venture Capital',
    value: MarketValue.VENTURE_CAPITAL,
    abbr: 'Venture Capital',
  },
  {
    label: 'Product Company',
    value: MarketValue.PRODUCT_COMPANY,
    abbr: 'Product Company',
  },
  {
    label: 'Private Equity / Hedge Fund / Acquirer',
    abbr: 'PE / HF / Ac',
    value: MarketValue.HF_PE_Ac,
  },
  {
    label: 'Government Office',
    abbr: 'Government Office',
    value: MarketValue.GOVERNMENT,
  },
];

export function getMarketByValue(value: string): Market {
  const marketFound = MARKETS.find((item) => item.value === value);

  if (!marketFound) {
    return MARKETS[0];
  }

  return marketFound;
}

export function MarketDropdown({
  name,
  rules,
  disabled,
  control,
  shouldUnregister,
  defaultValue,
  ...props
}: MarketDropdownProps) {
  const { field } = useController({
    name: name ?? MARKETS[0],
    rules,
    disabled,
    control,
    shouldUnregister,
    defaultValue,
  });

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        asChild
        onBlur={field.onBlur}
        name={field.name}
      >
        <Button
          variant="ghost"
          className="h-10 text-xs select-none"
          title={getMarketByValue(field.value).label}
          {...props}
          value={field.value}
          disabled={field.disabled}
        >
          <span className="font-bold">
            {getMarketByValue(field.value).abbr}
          </span>

          <ChevronDown />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent>
        {MARKETS.map((item) => (
          <DropdownMenuItem
            key={item.value}
            onSelect={() => {
              field.onChange(item.value);
            }}
          >
            {item.value === field.value ? (
              <CheckIcon />
            ) : (
              <span className="h-[16px] w-[16px]" />
            )}

            {item.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
