import { Card, CardContent, CardHeader, CardTitle } from '@/components/card';
import { cn } from '@/lib/styles/utils';

type IndicatorCardProps = {
  name: string;
  indicator: string;
  className?: string;
  color?: 'primary' | 'secondary';
};

export function IndicatorCard({
  className,
  indicator,
  name,
  color = 'primary',
}: IndicatorCardProps) {
  return (
    <Card className={cn('max-w-[260px]', className)}>
      <CardHeader>
        <CardTitle>{name}</CardTitle>
      </CardHeader>

      <CardContent
        className={cn(
          'text-5xl font-bold',
          color === 'primary' ? 'text-[#3769DA]' : 'text-slate-700',
        )}
      >
        {indicator}
      </CardContent>
    </Card>
  );
}
