import { cn } from '@/lib/styles/utils';
import { Card } from '@/components/card';
import { MarketDropdown } from './market-dropdown';
import { Command, CommandInput } from '@/components/command';
import { useController, UseControllerProps } from 'react-hook-form';
import { SearchResults } from '@/app/(private)/(root)/main/home/<USER>/search-results/search-results';

type SearchBoxProps = {
  className?: string;
} & UseControllerProps;

export function SearchBox({ className, ...controllerProps }: SearchBoxProps) {
  const { field } = useController({
    control: controllerProps.control,
    disabled: controllerProps.disabled,
    rules: controllerProps.rules,
    defaultValue: controllerProps.defaultValue,
    shouldUnregister: controllerProps.shouldUnregister,
    name: controllerProps.name ?? 'searchbox',
  });

  return (
    <Card className="search-box max-w-2xl mx-auto">
      <div className="p-1">
        <div className={cn('search-box-wrapper', className)}>
          <div className="box-wrapper flex items-center">
            <Command shouldFilter={false}>
              <div className="wrapper flex w-full items-center">
                <CommandInput
                  placeholder="Market Search"
                  className="!border-none"
                  onValueChange={field.onChange}
                  value={field.value}
                />

                <MarketDropdown name="market" />
              </div>

              <SearchResults />
            </Command>
          </div>
        </div>
      </div>
    </Card>
  );
}
