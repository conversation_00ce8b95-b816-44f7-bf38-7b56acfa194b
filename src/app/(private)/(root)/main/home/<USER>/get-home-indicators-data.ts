'use server';

import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/config/nextauth.config';
import { redirect } from 'next/navigation';
import { config } from '@/lib/config';

type HomeIndicators = {
  markets: number;
  customers: number;
  companies: number;
  contracts: number;
  opportunities: number;
};

export async function getHomeIndicatorsData(): Promise<HomeIndicators> {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/');
  }

  const configBaseUrl = config.api.base_url_for_server_side;
  const url = `${configBaseUrl}/market/indicators`;

  try {
    const request = await fetch(url, {
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
      },
    });

    return await request.json();
  } catch (e) {
    console.log(e);

    return new Promise<HomeIndicators>((resolve) => {
      resolve({
        markets: 809,
        companies: 1203,
        contracts: 5093,
        customers: 5293,
        opportunities: 3283,
      });
    });
  }
}
