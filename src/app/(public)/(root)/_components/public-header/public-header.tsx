import Link from 'next/link';
import { But<PERSON> } from '@/components/button/button';
import { LoginButton } from '@/app/(public)/(root)/_components/login-button/login-button';
import { getServerSession } from 'next-auth';
import { HighGroundLogo } from '@/components/icons/high-ground-logo';
import { LogOutButton } from '../log-out-button';

export async function PublicHeader() {
  const serverSession = await getServerSession();
  const isSignedIn = !!serverSession;

  return (
    <header className="bg-amber-300 h-[64px]">
      <nav className="h-full flex px-6 max-w-[1440px] mx-auto">
        <ul className="flex h-full w-full items-center ml-auto [&>li+li]:ml-2">
          <li className="mr-auto">
            <Button
              asChild
              size="lg"
              variant="link"
            >
              <Link
                href="/"
                className="w-[200px] h-[50px]"
              >
                <HighGroundLogo />
              </Link>
            </Button>
          </li>

          <li>{!isSignedIn && <LoginButton />}</li>

          <li>{isSignedIn && <LogOutButton />}</li>

          <li>
            <Button
              asChild
              variant="destructive"
            >
              {isSignedIn && (
                <Link href="/main/home?market=GOVERNMENT">Home</Link>
              )}
            </Button>
          </li>
        </ul>
      </nav>
    </header>
  );
}
