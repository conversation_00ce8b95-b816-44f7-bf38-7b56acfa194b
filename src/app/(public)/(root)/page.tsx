import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/config/nextauth.config';

export default async function PublicPage() {
  const session = await getServerSession(authOptions);

  // Only redirect to protected route if user has a valid session
  if (session && !session.error) {
    redirect('/main/home?market=VENTURE_CAPITAL');
  }

  // If no valid session, redirect to login
  redirect('/login');
}
