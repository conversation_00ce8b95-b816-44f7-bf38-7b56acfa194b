'use client';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/select';
import { cn } from '@/lib/styles/utils';
import { useController, UseControllerProps } from 'react-hook-form';
import { FormControl } from '@/components/forms/form';
import { ComponentProps } from 'react';
import { MARKETS } from '@/app/(private)/(root)/main/home/<USER>/market-dropdown';
import { Market } from '@/app/(private)/(root)/main/market/_lib/types/market-segment.type';

type SelectUseProps = ComponentProps<'select'> &
  UseControllerProps & { onUseSelect?: (use: Market | undefined) => void };

const UseList = MARKETS;

export function SelectUse({
  className,
  name,
  control,
  onUseSelect,
  rules,
}: SelectUseProps) {
  const { field } = useController({
    name,
    control,
    rules,
  });

  return (
    <Select
      onValueChange={(value: string) => {
        field.onChange(value);

        const itemFound = UseList.find((item) => item.value === value);

        if (onUseSelect) {
          onUseSelect(itemFound);
        }
      }}
      defaultValue={field.value}
      value={field.value}
    >
      <FormControl>
        <SelectTrigger
          className={cn('w-full', className)}
          onBlur={field.onBlur}
        >
          <SelectValue placeholder="Select an option" />
        </SelectTrigger>
      </FormControl>

      <SelectContent>
        {UseList.map((item) => {
          return (
            <SelectItem
              key={item.value}
              value={item.value}
            >
              {item.label}
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );
}
