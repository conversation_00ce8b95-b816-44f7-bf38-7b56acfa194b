'use client';

import { Button, Input, Label } from '@/components';
import { FormProvider, useForm } from 'react-hook-form';
import { NewAccountFormType } from '@/app/(public)/signup/_components/new-account-form/lib/types/new-account-form';
import { useSignUp } from '@/app/(public)/signup/_components/new-account-form/hooks/use-sign-up';
import { useToast } from '@/components/toast';
import Link from 'next/link';
import { SelectUse } from '@/app/(public)/signup/_components/select-use/select-use';
import { signIn } from 'next-auth/react';

export function NewAccountForm() {
  const methods = useForm<NewAccountFormType>({ mode: 'all' });
  const { signUp, isMutating } = useSignUp();
  const { toast } = useToast();

  const onSubmit = async (data: NewAccountFormType) => {
    const { confirmPassword, ...input } = data;

    try {
      if (
        !methods.formState.isValid ||
        isMutating ||
        confirmPassword !== data.password
      ) {
        return;
      }

      const response = await signUp({
        ...input,
        organizationSize: +input.organizationSize,
      });

      if (!(response as any)['ok']) {// eslint-disable-line
        toast({
          title: 'Error',
          description: 'There was an error',
          variant: 'destructive',
        });
        return;
      }

      toast({
        title: 'Success',
        description: 'Account has been created successfully',
      });

      methods.reset();

      setTimeout(async () => {
        await signIn('keycloak', {
          callbackUrl: '/main/home?market=VENTURE_CAPITAL&search=',
        });
      }, 1000);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="mx-auto w-full p-6 bg-white rounded-md">
      <FormProvider {...methods}>
        <form
          className="space-y-6"
          onSubmit={methods.handleSubmit(onSubmit)}
        >
          <div className="account-information [&>div.row+div.row]:mt-4">
            <h2 className="mb-4 text-[#3769DA] text-base font-bold">
              Account Information{' '}
            </h2>

            <div className="row flex gap-x-6">
              <div className="form-control basis-1/2 max-w-[50%] flex-grow-0 shrink">
                <Label
                  className="mb-2"
                  htmlFor="username"
                >
                  Username
                </Label>

                <Input
                  type="text"
                  id="username"
                  placeholder="Enter username"
                  required
                  rules={{ required: true }}
                  name="username"
                />
              </div>

              <div className="form-control basis-1/2 max-w-[50%] flex-grow-0 shrink">
                <Label
                  className="mb-2"
                  htmlFor="email"
                >
                  Email Address
                </Label>

                <Input
                  type="email"
                  id="email"
                  autoComplete="email"
                  placeholder="<EMAIL>"
                  required
                  rules={{ required: true }}
                  name="email"
                />
              </div>
            </div>

            <div className="row flex gap-x-6">
              <div className="form-control basis-1/2 max-w-[50%] flex-grow-0 shrink">
                <Label
                  className="mb-2"
                  htmlFor="firstName"
                >
                  First Name
                </Label>

                <Input
                  type="text"
                  id="firstName"
                  required
                  placeholder="Enter First Name"
                  rules={{ required: true }}
                  name="firstName"
                />
              </div>

              <div className="form-control basis-1/2 max-w-[50%] flex-grow-0 shrink">
                <Label
                  className="mb-2"
                  htmlFor="lastName"
                >
                  Last Name
                </Label>

                <Input
                  type="text"
                  id="lastName"
                  required
                  placeholder="Enter Last Name"
                  rules={{ required: true }}
                  name="lastName"
                />
              </div>
            </div>

            <div className="row flex gap-x-6">
              <div className="form-control basis-1/2 max-w-[50%] flex-grow-0 shrink">
                <Label
                  htmlFor="password"
                  className="mb-2"
                >
                  Password
                </Label>

                <Input
                  type="password"
                  id="password"
                  autoComplete="current-password"
                  required
                  rules={{ required: true }}
                  name="password"
                />
              </div>

              <div className="form-control basis-1/2 max-w-[50%] flex-grow-0 shrink">
                <Label
                  htmlFor="confirmPassword"
                  className="mb-2"
                >
                  Confirm Password
                </Label>

                <Input
                  type="password"
                  id="confirmPassword"
                  autoComplete="current-password"
                  required
                  rules={{
                    required: true,
                    validate: (value, formValues) =>
                      value !== formValues.password ? 'Not Valid' : true,
                  }}
                  name="confirmPassword"
                />

                {!!methods.formState.errors.confirmPassword?.message && (
                  <p className="text-orange-700 text-sm mt-2">
                    Make sure your passwords match
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="organization [&>div.row+div.row]:mt-4">
            <h2 className="mb-4 text-[#3769DA] text-base font-bold">
              Organization
            </h2>

            <div className="row flex gap-x-6">
              <div className="form-control basis-1/2 max-w-[50%] flex-grow-0 shrink">
                <Label
                  htmlFor="organizationName"
                  className="mb-2"
                >
                  Organization Name
                </Label>

                <Input
                  id="organizationName"
                  required
                  placeholder="Enter Organization Name"
                  rules={{ required: true }}
                  name="organizationName"
                />
              </div>

              <div className="form-control basis-1/2 max-w-[50%] flex-grow-0 shrink">
                <Label
                  htmlFor="organizationSize"
                  className="mb-2"
                >
                  Organization Size
                </Label>

                <Input
                  type="number"
                  id="organizationSize"
                  required
                  placeholder="Enter Organization Size"
                  rules={{ required: true }}
                  name="organizationSize"
                />
              </div>
            </div>

            <div className="row flex gap-x-6">
              <div className="form-control basis-1/2 max-w-[50%] flex-grow-0 shrink">
                <Label
                  htmlFor="organizationLocation"
                  className="mb-2"
                >
                  Organization Location
                </Label>

                <Input
                  id="organizationLocation"
                  placeholder="Enter Location"
                  required
                  rules={{ required: true }}
                  name="organizationLocation"
                />
              </div>

              <div className="form-control basis-1/2 max-w-[50%] flex-grow-0 shrink">
                <Label
                  htmlFor="organizationUse"
                  className="mb-2"
                >
                  Use
                </Label>

                <SelectUse
                  id="organizationUse"
                  name="organizationUse"
                  rules={{ required: true }}
                />
              </div>
            </div>
          </div>

          <div className="buttons-wrapper flex justify-end gap-x-4">
            <Button
              variant="hg_outline"
              asChild
            >
              <Link href="https://highground.market/">Go Back</Link>
            </Button>

            <Button
              className=""
              variant="hg_default"
              type="submit"
              disabled={!methods.formState.isValid || isMutating}
            >
              Sign Up
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
}
