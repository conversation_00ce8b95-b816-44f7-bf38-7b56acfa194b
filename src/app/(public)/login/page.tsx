import { Metadata } from 'next';
import { getProviders } from 'next-auth/react';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { SignProviders } from '@/app/(public)/login/_components';
import { SearchParams } from 'next/dist/server/request/search-params';
import { Button } from '@/components/button/button';
import Link from 'next/link';
import { authOptions } from '@/lib/config/nextauth.config';

export const metadata: Metadata = {
  title: 'Sign In - HighGround',
};

export default async function LoginPage({
  searchParams,
}: {
  searchParams: Promise<SearchParams>;
}) {
  const providers = await getProviders();
  const session = await getServerSession(authOptions);
  const sParams = await searchParams;
  const redirectUrl = sParams['callbackUrl'] as string;

  // Only redirect if session is valid (no error)
  if (session && !session.error) {
    redirect(redirectUrl ?? '/main/home?market=VENTURE_CAPITAL');
  }

  return (
    <div className="sign-in-page">
      <div className="page-wrapper h-screen flex items-center justify-center bg-[#E4E8EF]">
        <div className="sign-in-providers w-[400px] rounded min-h-[340px] bg-white shadow-lg p-6 flex flex-col">
          <h1 className="text-3xl font-bold text-center mb-6 tracking-tight text-[#3769DA]">
            Sign In
          </h1>

          <p className="text-base text-slate-500 text-center mb-4">
            Sign into your account
          </p>

          <SignProviders
            providers={providers}
            className="mb-6"
          />

          <p className="text-base text-slate-500 text-center mb-4">
            Don’t have an account yet?
          </p>

          <Button
            variant="hg_default"
            className="text-sm leading-6 h-[40px] mb-4"
            asChild
          >
            <Link href="/signup">Register</Link>
          </Button>

          <div className="footer text-center">
            <Button
              asChild
              variant="hg_ghost"
              className="text-base leading-6 w-full h-[40px]"
            >
              <Link href="https://highground.market/">Cancel</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
