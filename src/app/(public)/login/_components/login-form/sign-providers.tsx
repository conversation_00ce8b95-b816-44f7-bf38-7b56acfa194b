'use client';

import { ClientSafeProvider, LiteralUnion, signIn } from 'next-auth/react';
import { Button } from '@/components/button';
import { BuiltInProviderType } from 'next-auth/providers/index';
import { cn } from '@/lib/styles/utils';
import { KeycloakLogo } from '@/components/icons/keycloak-logo';

export function SignProviders({
  providers,
  className,
}: {
  className?: string;
  providers: Record<
    LiteralUnion<BuiltInProviderType, string>,
    ClientSafeProvider
  > | null;
  callbackUrl?: string;
}) {
  return (
    <div
      className={cn(
        'wrapper [&>button+button]:mt-4 flex flex-col w-full',
        className,
      )}
    >
      {Object.values(providers ?? []).map((provider) => (
        <Button
          variant="hg_outline"
          key={provider.id}
          size="lg"
          onClick={() => signIn(provider.id)}
          className="w-full"
        >
          <KeycloakLogo /> {provider.name}
        </Button>
      ))}
    </div>
  );
}
