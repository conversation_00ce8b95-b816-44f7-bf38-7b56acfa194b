import './globals.scss';
import { Inter, Orbitron } from 'next/font/google';
import SessionProvider from '@/lib/providers/session-provider-wrapper';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/config/nextauth.config';
import { Toaster } from '@/components/toast/toaster';
import { ReactNode } from 'react';

const inter = Inter({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
});

const orbitron = Orbitron({
  weight: ['400', '500', '600', '700', '800', '900'],
  subsets: ['latin'],
  variable: '--orbitron'
});

export default async function RootLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  const session = await getServerSession(authOptions);

  return (
    <html lang="en" suppressHydrationWarning={true}>
      <body
        className={`${inter.className} ${orbitron.variable} antialiased`}
        suppressHydrationWarning={true}
      >
        <div className="overflow-x-hidden">
          <SessionProvider session={session}>{children}</SessionProvider>
          <Toaster />
        </div>
      </body>
    </html>
  );
}
