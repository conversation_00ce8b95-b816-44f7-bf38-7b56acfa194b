#!/bin/bash

check_error() {
  if [ $? -ne 0 ]; then
    echo "Error: $1"
    exit 1
  fi
}

install_dependencies() {
  echo "📦 Installing dependencies..."
  npm install
  check_error "Failed to install dependencies."
  }

start_dev_server(){
  echo "🚀 Starting the development server.."
  npm run dev
  check_error "Failed to start the development server."
}

main() {
  install_dependencies
  start_dev_server
}

main
