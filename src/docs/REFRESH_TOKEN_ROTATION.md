# Refresh Token Rotation Implementation

This document describes the refresh token rotation implementation for NextAuth.js with Keycloak.

## Overview

The implementation provides automatic token refresh functionality that:
- Automatically refreshes access tokens when they expire
- Handles refresh token rotation securely
- Provides proper error handling when refresh fails
- Maintains session continuity for users
- Uses proper TypeScript typing throughout

## Key Components

### 1. NextAuth Configuration (`src/lib/config/nextauth.config.ts`)

The main configuration includes:
- `refreshAccessToken()` function that handles token refresh with Keycloak
- Enhanced JWT callback that stores refresh tokens and expiration times
- Session callback that passes tokens and errors to the client

### 2. Type Definitions (`src/lib/types/next-auth.d.ts`)

Extends NextAuth's default types to include:
- `accessToken`, `refreshToken`, `accessTokenExpires` in JWT
- `accessToken`, `id_token`, `error` in Session
- Proper TypeScript support throughout the application

### 3. API Proxy Route (`src/app/api/proxy/route.ts`)

Server-side proxy that:
- Handles all backend API calls with fresh tokens
- Automatically uses refreshed tokens from server session
- Eliminates client/server token sync issues
- Provides seamless authentication for all API calls

### 4. Auth Error Handler (`src/lib/hooks/use-auth-error-handler.tsx`)

Client-side hook that:
- Monitors session for refresh token errors
- Automatically redirects to sign-in when refresh fails
- Can be used in any component that needs auth error handling

### 5. Enhanced Session Provider (`src/lib/providers/session-provider-wrapper.tsx`)

Wrapper around NextAuth's SessionProvider that:
- Automatically refetches session every 5 minutes
- Refetches session when window regains focus
- Keeps sessions fresh and responsive

### 6. Updated Hooks

Both `useSwrAuth` and `useSWRMutateWithAuth` have been updated to:
- Use the API proxy for all backend calls
- Handle both full URLs and paths automatically
- Include automatic auth error handling
- Provide seamless token refresh without client-side token management

## Configuration Requirements

### Environment Variables

Ensure these environment variables are set:
```env
KEYCLOAK_CLIENT_ID=your_client_id
KEYCLOAK_CLIENT_SECRET=your_client_secret
KEYCLOAK_ISSUER=https://your-keycloak-domain/realms/your-realm
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=https://your-app-domain
```

### Keycloak Configuration

In Keycloak Admin Console:
1. Navigate to your client's **Advanced** tab
2. Set "Allow refresh token in Standard Token Exchange" to "Yes"
3. Configure appropriate token lifespans:
   - Access Token Lifespan: 1-5 minutes (for testing)
   - Refresh Token Lifespan: 1 day or longer
   - SSO Session Idle: As needed for your use case

## Usage Examples

### Basic Usage in Components

```tsx
import { useAuthErrorHandler } from '@/lib/hooks/use-auth-error-handler';

function MyComponent() {
  useAuthErrorHandler(); // Automatically handles auth errors
  
  // Your component logic
  return <div>My Component</div>;
}
```

### Using Auth Error Boundary

```tsx
import { AuthErrorBoundary } from '@/lib/components/auth-error-boundary';

function App() {
  return (
    <AuthErrorBoundary>
      <MyProtectedContent />
    </AuthErrorBoundary>
  );
}
```

### Using SWR with Auth

```tsx
import { useSwrAuth } from '@/lib/hooks/use-swr-auth';

function DataComponent() {
  const { data, isLoading, error } = useSwrAuth<MyDataType>('/api/my-data');
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading data</div>;
  
  return <div>{JSON.stringify(data)}</div>;
}
```

## How It Works

### Server-Side Token Management
1. **Initial Sign-in**: When user signs in, JWT callback stores access token, refresh token, and expiration time
2. **Token Check**: On each API request, JWT callback checks if access token has expired
3. **Automatic Refresh**: If expired, `refreshAccessToken()` is called to get new tokens
4. **Fresh Tokens**: Server session always has the latest tokens

### Client-Side API Calls
1. **Proxy Route**: All client API calls go through `/api/proxy` instead of directly to backend
2. **Server Session**: Proxy route uses `getServerSession()` to get fresh tokens
3. **Backend Request**: Proxy forwards request to backend with current access token
4. **Seamless Experience**: Client never needs to handle token refresh directly

### Error Handling
1. **Refresh Failures**: If refresh fails, error is passed to client-side session
2. **Client Detection**: `useAuthErrorHandler` detects the error and redirects to sign-in
3. **Graceful Fallback**: Users are smoothly redirected without seeing errors

## Security Considerations

- Refresh tokens are stored securely in JWT (server-side only)
- Access tokens have short lifespans to minimize exposure
- Failed refresh attempts are logged for monitoring
- Users are automatically redirected to sign-in when refresh fails

## Monitoring and Debugging

- Check browser console for refresh token errors
- Monitor server logs for failed refresh attempts
- Use browser dev tools to inspect session data
- Verify Keycloak token settings match your requirements

## Testing

To test the implementation:
1. Set a very short access token lifespan in Keycloak (1 minute)
2. Sign in to your application
3. Wait for the token to expire
4. Perform an action that requires authentication
5. Verify that the token is automatically refreshed
6. Test with an invalid refresh token to ensure error handling works
