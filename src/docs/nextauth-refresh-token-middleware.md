
# ✅ Middleware-Based Refresh Token Rotation with NextAuth.js + Keycloak

## Overview

This implementation follows the **community recommended middleware approach** for handling refresh token rotation in **NextAuth.js** using **Keycloak**. It ensures secure, race-condition-free, and seamless session management across all routes and tabs, without client-server sync issues.

---

## 🔧 Key Features

- ✅ Handles token refresh server-side via middleware  
- ✅ Works across all pages and API routes  
- ✅ Prevents race conditions and multi-tab conflicts  
- ✅ Seamless user experience — no visible session interruptions  
- ✅ Strong TypeScript typing throughout  
- ✅ Full error handling and graceful fallback behavior  
- ✅ Secure storage and rotation of refresh tokens

---

## 🧱 Core Components

### `middleware.ts`
- Intercepts requests to protected routes
- Checks token expiration and refreshes tokens if needed
- Updates session cookies with fresh tokens
- Redirects to login if refresh fails

### `nextauth.config.ts`
- Defines `refreshAccessToken()` using Keycloak
- JWT callback stores `accessToken`, `refreshToken`, and expiration
- Session callback exposes token info and errors to client

### `next-auth.d.ts`
- Extends types for `JWT` and `Session` to include:
  - `accessToken`, `refreshToken`, `accessTokenExpires`, `id_token`, `error`

### Client-side
- `useAuthErrorHandler()` – monitors session errors and redirects to login  
- `AuthErrorBoundary` – wraps protected components with fallback UI  
- `SessionProviderWrapper` – auto-refreshes session every 5 mins and on window focus  
- `useSwrAuth()` – SWR hook that handles token-based requests cleanly  

---

## ⚙️ Configuration

### Required Environment Variables

```env
KEYCLOAK_CLIENT_ID=your_client_id
KEYCLOAK_CLIENT_SECRET=your_client_secret
KEYCLOAK_ISSUER=https://your-keycloak-domain/realms/your-realm
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=https://your-app-domain
```

### Keycloak Setup

- Enable **Refresh Token in Standard Token Exchange**
- Recommended Token Lifespans:
  - Access Token: 1–5 minutes
  - Refresh Token: 30 minutes–1 day
  - SSO Idle Timeout: Based on use case

---

## 🧪 Testing Scenarios

### ✅ Single Tab
- Wait for access token to expire
- Navigate through the app
- ✅ Session refreshes automatically

### ✅ Multi-Tab
- Open 2+ tabs
- Wait for expiration
- Switch between tabs
- ✅ Seamless session, no logout

### ✅ Expired Refresh Token
- Wait until refresh token expires
- Try to navigate
- ✅ Auto redirect to login

---

## 🧠 How It Works

```
Request → Middleware → Token Expiry Check
   → If expired → refreshAccessToken() → Update Cookie
   → Continue → Page or API
```

- One refresh per request ensures race-condition-free handling  
- Middleware acts as the **central auth gate** across your app  
- Updated tokens are shared across all tabs via cookies

---

## 🔒 Security Highlights

- Access tokens: short-lived  
- Refresh tokens: stored server-side only  
- Cookies: HttpOnly + Secure flags  
- Logs and monitors refresh failures  
- Automatic logout on refresh failure  

---

## ✅ Production Ready

- ✅ Used in large-scale NextAuth.js deployments  
- ✅ Eliminates need for client-side refresh logic  
- ✅ Improves performance by avoiding API proxy hops  
- ✅ Strong community support and long-term maintainability
