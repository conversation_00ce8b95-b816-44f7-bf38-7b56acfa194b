'use client';

import { SessionProvider } from 'next-auth/react';
import { ReactNode } from 'react';

interface SessionProviderWrapperProps {
  children: ReactNode;
  session?: any;
}

export default function SessionProviderWrapper({
  children,
  session
}: SessionProviderWrapperProps) {
  return (
    <SessionProvider
      session={session}
      // Refetch session every 5 minutes to keep it fresh
      refetchInterval={5 * 60}
      // Refetch on window focus
      refetchOnWindowFocus={true}
    >
      {children}
    </SessionProvider>
  );
}
