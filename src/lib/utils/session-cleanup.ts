import { NextRequest, NextResponse } from 'next/server';

/**
 * Clears all NextAuth session cookies and redirects to login
 * This is more aggressive than the standard signOut to handle stale sessions
 */
export function clearStaleSession(request: NextRequest): NextResponse {
  console.log('Clearing stale session and redirecting to login');

  const response = NextResponse.redirect(new URL('/login', request.url));

  // Clear all possible NextAuth cookies
  const cookiesToClear = [
    'next-auth.session-token',
    '__Secure-next-auth.session-token',
    'next-auth.csrf-token',
    '__Host-next-auth.csrf-token',
    'next-auth.callback-url',
    '__Secure-next-auth.callback-url',
    'next-auth.pkce.code_verifier',
    '__Secure-next-auth.pkce.code_verifier'
  ];

  cookiesToClear.forEach(cookieName => {
    // Delete the cookie
    response.cookies.delete(cookieName);

    // Set expired cookie with multiple configurations to ensure cleanup
    response.cookies.set(cookieName, '', {
      expires: new Date(0),
      maxAge: 0,
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    });

    // Also try with root path
    response.cookies.set(cookieName, '', {
      expires: new Date(0),
      maxAge: 0,
      path: '/',
      domain: undefined,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    });
  });

  // Add cache control headers to prevent caching
  response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
  response.headers.set('Pragma', 'no-cache');
  response.headers.set('Expires', '0');

  return response;
}

/**
 * Checks if a token appears to be stale/corrupted
 */
export function isStaleToken(token: unknown): boolean {
  // No token at all
  if (!token) return true;

  // Token structure seems corrupted
  if (typeof token !== 'object') return true;

  // Type guard to check if token has the expected properties
  const tokenObj = token as Record<string, unknown>;

  // Has refresh error
  if (tokenObj.error === 'RefreshAccessTokenError') return true;

  // Missing essential fields
  if (!tokenObj.accessToken && !tokenObj.refreshToken) return true;

  // Check if refresh token is very old (more than 24 hours)
  const now = Date.now();
  const refreshTokenExpires = tokenObj.refreshTokenExpires as number;
  if (refreshTokenExpires && now > refreshTokenExpires) {
    console.log('Refresh token has expired');
    return true;
  }

  // Check if access token is very old (more than 24 hours past expiry)
  const accessTokenExpires = tokenObj.accessTokenExpires as number;
  if (accessTokenExpires) {
    const tokenAge = now - accessTokenExpires;
    const maxStaleAge = 24 * 60 * 60 * 1000; // 24 hours
    if (tokenAge > maxStaleAge) {
      console.log('Access token is too old, considering stale');
      return true;
    }
  }

  return false;
}
