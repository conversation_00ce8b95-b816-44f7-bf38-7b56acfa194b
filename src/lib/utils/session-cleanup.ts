import { NextRequest, NextResponse } from 'next/server';

/**
 * Clears all NextAuth session cookies and redirects to login
 * This is more aggressive than the standard signOut to handle stale sessions
 */
export function clearStaleSession(request: NextRequest): NextResponse {
  const response = NextResponse.redirect(new URL('/login', request.url));
  
  // Clear all possible NextAuth cookies
  const cookiesToClear = [
    'next-auth.session-token',
    '__Secure-next-auth.session-token',
    'next-auth.csrf-token', 
    '__Host-next-auth.csrf-token',
    'next-auth.callback-url',
    '__Secure-next-auth.callback-url',
    'next-auth.pkce.code_verifier',
    '__Secure-next-auth.pkce.code_verifier'
  ];

  cookiesToClear.forEach(cookieName => {
    response.cookies.delete(cookieName);
    // Also try to delete with different path and domain settings
    response.cookies.set(cookieName, '', {
      expires: new Date(0),
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    });
  });

  return response;
}

/**
 * Checks if a token appears to be stale/corrupted
 */
export function isStaleToken(token: unknown): boolean {
  // No token at all
  if (!token) return true;

  // Token structure seems corrupted
  if (typeof token !== 'object') return true;

  // Type guard to check if token has the expected properties
  const tokenObj = token as Record<string, unknown>;

  // Has refresh error
  if (tokenObj.error === 'RefreshAccessTokenError') return true;

  // Missing essential fields
  if (!tokenObj.accessToken && !tokenObj.refreshToken) return true;

  return false;
}
