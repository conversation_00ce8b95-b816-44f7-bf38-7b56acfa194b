import { AuthOptions } from 'next-auth';
import KeycloakProvider from 'next-auth/providers/keycloak';
import { JWT } from 'next-auth/jwt';

/**
 * Takes a token, and returns a new token with updated
 * `accessToken` and `accessTokenExpires`. If an error occurs,
 * returns the old token and an error property
 */
export async function refreshAccessToken(token: JWT): Promise<JWT> {
  try {
    const url = `${process.env.KEYCLOAK_ISSUER}/protocol/openid-connect/token`;

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      method: 'POST',
      body: new URLSearchParams({
        client_id: process.env.KEYCLOAK_CLIENT_ID!,
        client_secret: process.env.KEYCLOAK_CLIENT_SECRET!,
        grant_type: 'refresh_token',
        refresh_token: token.refreshToken as string,
      }),
    });

    const refreshedTokens = await response.json();

    if (!response.ok) {
      // Log specific error for debugging
      if (refreshedTokens.error === 'invalid_grant' && refreshedTokens.error_description === 'Token is not active') {
        console.log('Refresh token has expired or is invalid, user needs to re-authenticate');
      } else {
        console.error('Error refreshing access token:', refreshedTokens);
      }
      throw refreshedTokens;
    }

    // Clear any previous errors on successful refresh
    return {
      ...token,
      accessToken: refreshedTokens.access_token,
      accessTokenExpires: Date.now() + (refreshedTokens.expires_in * 1000),
      refreshToken: refreshedTokens.refresh_token ?? token.refreshToken, // Fall back to old refresh token
      refreshTokenExpires: refreshedTokens.refresh_expires_in ? Date.now() + (refreshedTokens.refresh_expires_in * 1000) : token.refreshTokenExpires,
      expiresIn: refreshedTokens.expires_in, // Update the expires_in value
      error: undefined, // Clear any previous errors
    };
  } catch {
    return {
      ...token,
      error: 'RefreshAccessTokenError',
    };
  }
}

export const authOptions: AuthOptions = {
  secret: process.env.NEXTAUTH_SECRET ?? '',
  providers: [
    KeycloakProvider({
      clientId: process.env.KEYCLOAK_CLIENT_ID ?? '',
      clientSecret: process.env.KEYCLOAK_CLIENT_SECRET ?? '',
      issuer: process.env.KEYCLOAK_ISSUER ?? '',
    }),
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      // Initial sign in
      if (account && user) {
        return {
          ...token,
          accessToken: account.access_token,
          accessTokenExpires: Date.now() + ((account.expires_in as number) * 1000),
          refreshToken: account.refresh_token,
          refreshTokenExpires: account.refresh_expires_in ? Date.now() + ((account.refresh_expires_in as number) * 1000) : undefined,
          expiresIn: account.expires_in, // Store the original expires_in value
          id_token: account.id_token,
          user,
        };
      }

      // Return previous token if the access token has not expired yet
      if (Date.now() < (token.accessTokenExpires as number)) {
        return token;
      }

      // Access token has expired, try to update it
      return refreshAccessToken(token);
    },
    async session({ session, token }) {
      if (token) {
        if (token.user) {
          session.user = token.user;
        }
        session.accessToken = token.accessToken;
        session.id_token = token.id_token;
        session.error = token.error;
      }

      return session;
    },
  },
  pages: {
    signIn: '/login',
  },
};
