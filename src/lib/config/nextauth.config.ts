import { AuthOptions } from 'next-auth';
import KeycloakProvider from 'next-auth/providers/keycloak';

export const authOptions: AuthOptions = {
  secret: process.env.NEXTAUTH_SECRET ?? '',
  providers: [
    KeycloakProvider({
      clientId: process.env.KEYCLOAK_CLIENT_ID ?? '',
      clientSecret: process.env.KEYCLOAK_CLIENT_SECRET ?? '',
      issuer: process.env.KEYCLOAK_ISSUER ?? '',
    }),
  ],
  callbacks: {
    async jwt({ token, account }) {
      if (account) {
        token.accessToken = account.access_token;
        token.id_token = account.id_token;
      }

      return token;
    },
    async session({ session, token }) {
      (session as any).accessToken = token.accessToken; // eslint-disable-line
      (session as any).id_token = token.id_token; // eslint-disable-line

      return session;
    },
  },
  pages: {
    signIn: '/login',
  },
};
