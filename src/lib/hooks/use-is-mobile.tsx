import * as React from 'react';

const MOBILE_BREAKPOINT = 768;

export function useIsMobile() {
  // Start with false as default to match server-side rendering
  // This prevents hydration mismatches
  const [isMobile, setIsMobile] = React.useState<boolean>(false);
  const [isClient, setIsClient] = React.useState<boolean>(false);

  // Use useLayoutEffect to run synchronously after all DOM mutations
  // but before the browser paints, ensuring we get the correct initial state
  React.useLayoutEffect(() => {
    setIsClient(true);

    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    };

    // Set initial value
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);

    mql.addEventListener('change', onChange);
    return () => mql.removeEventListener('change', onChange);
  }, []);

  // During SSR and before client-side hydration, return false
  // After hydration, return the actual mobile state
  return isClient ? isMobile : false;
}
