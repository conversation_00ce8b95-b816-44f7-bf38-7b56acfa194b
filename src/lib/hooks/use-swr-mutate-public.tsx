'use client';

import useSWRMutation from 'swr/mutation';

/**
 * Public API mutation hook for endpoints that don't require authentication
 * Used for signup, password reset, etc.
 */
async function publicUpdater(
  url: string,
  { arg }: { arg: unknown }
) {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
    },
    method: 'POST',
    body: JSON.stringify(arg),
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
}

export function useSWRMutatePublic(urlToUse: string) {
  const { data, isMutating, error, trigger } = useSWRMutation<unknown, Error, string, unknown>(
    urlToUse,
    publicUpdater,
  );

  return { data, isMutating, error, trigger };
}
