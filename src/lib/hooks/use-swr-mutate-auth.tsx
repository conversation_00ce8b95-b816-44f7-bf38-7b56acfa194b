'use client';

import { signOut, useSession } from 'next-auth/react';
import useSWRMutation from 'swr/mutation';
import { useAuthErrorHandler } from './use-auth-error-handler';

async function updater(
  url: string,
  { sessionToken, data }: { sessionToken: string, data: unknown},
) {
  const response = await fetch(url, {
    headers: {
      Authorization: `Bearer ${sessionToken}`,
      'Content-Type': 'application/json',
    },
    method: 'post',
    body: JSON.stringify(data),
  });

  if (response.status === 401) {
    signOut();
    return;
  }

  return response.json();
}

export function useSWRMutateWithAuth(urlToUse: string) {
  const { data: session } = useSession();
  useAuthErrorHandler(); // Handle refresh token errors

  const sessionToken = session?.accessToken ?? '';

  const { data, isMutating, error, trigger } = useSWRMutation<unknown, Error, string, unknown>(
    urlToUse,
    (url, {arg}: {arg: unknown}) => {
      return updater(url, {sessionToken, data: arg});
    },
  );

  return { data, isMutating, error, trigger };
}
