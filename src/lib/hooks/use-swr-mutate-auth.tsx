'use client';

import { signOut, useSession } from 'next-auth/react';
import useSWRMutation from 'swr/mutation';

async function updater(
  url: string,
  { sessionToken, data }: { sessionToken: string, data: unknown},
) {
  const response = await fetch(url, {
    headers: {
      Authorization: `Bearer ${sessionToken}`,
      'Content-Type': 'application/json',
    },
    method: 'post',
    body: JSON.stringify(data),
  });

  if (response.status === 401) {
    signOut();
    return;
  }

  return response.json();
}

export function useSWRMutateWithAuth(urlToUse: string) {
  const session = useSession();

  const sessionToken = session.data ? (session.data as any).accessToken as string : ''; //eslint-disable-line

  const { data, isMutating, error, trigger } = useSWRMutation<unknown, Error, string, unknown>(
    urlToUse,
    (url, {arg}: {arg: unknown}) => {
      return updater(url, {sessionToken, data: arg});
    },
  );

  return { data, isMutating, error, trigger };
}
