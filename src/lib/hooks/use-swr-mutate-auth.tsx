'use client';

import { useSession } from 'next-auth/react';
import useSWRMutation from 'swr/mutation';
import { useAuthErrorHandler } from './use-auth-error-handler';

function extractBackendPath(urlOrPath: string): string {
  // If it's already a path (starts with /), return as is
  if (urlOrPath.startsWith('/')) {
    return urlOrPath;
  }

  // If it's a full URL, extract the path
  try {
    const url = new URL(urlOrPath);
    return url.pathname + url.search;
  } catch {
    // If URL parsing fails, assume it's already a path
    return urlOrPath;
  }
}

async function mutateViaProxy(
  urlOrPath: string,
  { arg }: { arg: unknown }
) {
  const backendPath = extractBackendPath(urlOrPath);

  // Use the proxy API route which will handle authentication server-side
  const proxyUrl = `/api/proxy?path=${encodeURIComponent(backendPath)}`;

  const response = await fetch(proxyUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(arg),
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
}

export function useSWRMutateWithAuth(urlOrPath: string) {
  const { status } = useSession();
  useAuthErrorHandler(); // Handle refresh token errors

  const { data, isMutating, error, trigger } = useSWRMutation<unknown, Error, string, unknown>(
    urlOrPath,
    status === 'authenticated' ? mutateViaProxy : () => Promise.reject(new Error('Not authenticated'))
  );

  return { data, isMutating, error, trigger };
}
