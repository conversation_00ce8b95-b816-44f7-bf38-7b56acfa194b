'use client';

import useSWRMutation from 'swr/mutation';
import { authPost } from '@/lib/utils/auth-fetch';

export function useSWRMutateWithAuth(urlToUse: string) {
  const { data, isMutating, error, trigger } = useSWRMutation<unknown, Error, string, unknown>(
    urlToUse,
    (url, {arg}: {arg: unknown}) => {
      return authPost(url, arg);
    },
  );

  return { data, isMutating, error, trigger };
}
