'use client';

import useSWRImmutable from 'swr/immutable';
import { authFetchJson } from '@/lib/utils/auth-fetch';

export function useSwrAuth<T>(url: string) {
  const { data, isLoading, error } = useSWRImmutable(
    url,
    (url) => authFetchJson<T>(url),
    {
      // Add retry configuration for failed requests
      errorRetryCount: 2,
      errorRetryInterval: 1000, // 1 second between retries
    },
  );

  return { data, isLoading, error };
}
