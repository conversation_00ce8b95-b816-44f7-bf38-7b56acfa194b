'use client';

import { signOut, useSession } from 'next-auth/react';
import useSWRImmutable from 'swr/immutable';
import { useAuthErrorHand<PERSON> } from './use-auth-error-handler';

export function useSwrAuth<T>(url: string) {
  const { data: session } = useSession();
  useAuthErrorHandler(); // Handle refresh token errors

  const { data, isLoading, error } = useSWRImmutable(
    session?.accessToken ? url : null, // Only fetch if we have an access token
    (url) =>
      fetch(url, {
        headers: {
          Authorization: `Bearer ${session?.accessToken}`,
        },
      }).then((r: Response) => {
        if (r.status === 401) {
          signOut();
          return;
        }

        return r.json() as T;
      }),
    {},
  );

  return { data, isLoading, error };
}
