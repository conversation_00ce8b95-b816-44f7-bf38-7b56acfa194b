'use client';

import { signOut, useSession } from 'next-auth/react';
import useSWRImmutable from 'swr/immutable';

export function useSwrAuth<T>(url: string) {
  const session = useSession();

  const { data, isLoading, error } = useSWRImmutable(
    url,
    (url) =>
      fetch(url, {
        headers: {
          Authorization: `Bearer ${(session.data as any).accessToken}`, // eslint-disable-line
        },
      }).then((r: Response) => {
        if (r.status === 401) {
          signOut();
          return;
        }

        return r.json() as T;
      }),
    {},
  );

  return { data, isLoading, error };
}
