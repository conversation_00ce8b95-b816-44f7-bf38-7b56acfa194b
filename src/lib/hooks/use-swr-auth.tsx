'use client';

import { useSession } from 'next-auth/react';
import useSWRImmutable from 'swr/immutable';
import { useAuthErrorHandler } from './use-auth-error-handler';

function extractBackendPath(urlOrPath: string): string {
  // If it's already a path (starts with /), return as is
  if (urlOrPath.startsWith('/')) {
    return urlOrPath;
  }

  // If it's a full URL, extract the path
  try {
    const url = new URL(urlOrPath);
    return url.pathname + url.search;
  } catch {
    // If URL parsing fails, assume it's already a path
    return urlOrPath;
  }
}

async function fetchViaProxy<T>(urlOrPath: string): Promise<T> {
  const backendPath = extractBackendPath(urlOrPath);

  // Use the proxy API route which will handle authentication server-side
  const proxyUrl = `/api/proxy?path=${encodeURIComponent(backendPath)}`;

  const response = await fetch(proxyUrl);

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json() as T;
}

export function useSwrAuth<T>(urlOrPath: string) {
  const { status } = useSession();
  useAuthErrorHandler(); // Handle refresh token errors

  const { data, isLoading, error } = useSWRImmutable(
    // Only fetch if we have an authenticated session
    status === 'authenticated' ? urlOrPath : null,
    fetchViaProxy<T>,
    {
      // Retry on errors to handle temporary issues
      shouldRetryOnError: (error) => {
        // Don't retry on 401/403 errors (auth issues)
        return !error.message.includes('401') && !error.message.includes('403');
      },
      errorRetryCount: 2,
      errorRetryInterval: 1000,
    },
  );

  return { data, isLoading, error };
}
