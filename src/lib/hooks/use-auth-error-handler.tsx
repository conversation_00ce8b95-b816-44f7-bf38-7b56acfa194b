'use client';

import { signIn, useSession } from 'next-auth/react';
import { useEffect } from 'react';

/**
 * Hook to handle authentication errors, particularly refresh token failures.
 * Automatically redirects to sign-in when RefreshAccessTokenError occurs.
 */
export function useAuthErrorHandler() {
  const { data: session } = useSession();

  useEffect(() => {
    if (session?.error === 'RefreshAccessTokenError') {
      // Force sign in to hopefully resolve error
      signIn('keycloak');
    }
  }, [session]);

  return {
    hasAuthError: session?.error === 'RefreshAccessTokenError',
    error: session?.error,
  };
}
