'use client';

import { useAuthErrorHand<PERSON> } from '@/lib/hooks/use-auth-error-handler';
import { ReactNode } from 'react';

interface AuthErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Component that wraps children and handles authentication errors.
 * Shows a fallback UI when refresh token errors occur.
 */
export function AuthErrorBoundary({ 
  children, 
  fallback 
}: AuthErrorBoundaryProps) {
  const { hasAuthError } = useAuthErrorHandler();

  if (hasAuthError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        {fallback || (
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">Session Expired</h2>
            <p className="text-gray-600">
              Your session has expired. You will be redirected to sign in.
            </p>
          </div>
        )}
      </div>
    );
  }

  return <>{children}</>;
}
